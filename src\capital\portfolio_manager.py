"""
Portfolio Manager for AutoGPT Trader
Manages portfolio allocation and optimization
"""

import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class PortfolioManager:
    """
    Advanced portfolio management system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.portfolio = {}
        self.allocation_targets = {}
        self.rebalance_threshold = 0.05  # 5% threshold for rebalancing
        
        logger.info("📊 [PORTFOLIO] Portfolio manager initialized")
    
    async def get_portfolio_value(self) -> Decimal:
        """Get total portfolio value"""
        try:
            total_value = Decimal('0.0')
            for asset, data in self.portfolio.items():
                total_value += Decimal(str(data.get('value', 0)))
            return total_value
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error getting portfolio value: {e}")
            return Decimal('0.0')
    
    async def update_portfolio(self, asset: str, quantity: Decimal, price: Decimal) -> None:
        """Update portfolio with new asset data"""
        try:
            value = quantity * price
            self.portfolio[asset] = {
                'quantity': quantity,
                'price': price,
                'value': value,
                'last_update': datetime.now(timezone.utc).isoformat()
            }
            
            logger.debug(f"📊 [PORTFOLIO] Updated {asset}: {quantity} @ {price} = {value}")
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error updating portfolio: {e}")
    
    async def get_allocation_report(self) -> Dict[str, Any]:
        """Get portfolio allocation report"""
        try:
            total_value = await self.get_portfolio_value()
            
            if total_value == 0:
                return {'error': 'No portfolio value'}
            
            allocation_report = {
                'total_value': float(total_value),
                'assets': {},
                'allocation_percentages': {},
                'rebalance_needed': False
            }
            
            for asset, data in self.portfolio.items():
                asset_value = Decimal(str(data['value']))
                allocation_pct = float(asset_value / total_value)
                
                allocation_report['assets'][asset] = {
                    'value': float(asset_value),
                    'allocation_percentage': allocation_pct,
                    'quantity': float(data['quantity']),
                    'price': float(data['price'])
                }
                
                allocation_report['allocation_percentages'][asset] = allocation_pct
                
                # Check if rebalancing is needed
                target_allocation = self.allocation_targets.get(asset, 0)
                if abs(allocation_pct - target_allocation) > self.rebalance_threshold:
                    allocation_report['rebalance_needed'] = True
            
            return allocation_report
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error generating allocation report: {e}")
            return {'error': str(e)}
    
    async def set_allocation_targets(self, targets: Dict[str, float]) -> None:
        """Set target allocation percentages"""
        try:
            # Validate targets sum to 1.0
            total_allocation = sum(targets.values())
            if abs(total_allocation - 1.0) > 0.01:
                logger.warning(f"⚠️ [PORTFOLIO] Allocation targets sum to {total_allocation}, not 1.0")
            
            self.allocation_targets = targets
            logger.info(f"📊 [PORTFOLIO] Set allocation targets: {targets}")
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error setting allocation targets: {e}")
    
    async def calculate_rebalance_trades(self) -> List[Dict[str, Any]]:
        """Calculate trades needed for rebalancing"""
        try:
            allocation_report = await self.get_allocation_report()
            
            if allocation_report.get('error') or not allocation_report.get('rebalance_needed'):
                return []
            
            total_value = Decimal(str(allocation_report['total_value']))
            rebalance_trades = []
            
            for asset, target_allocation in self.allocation_targets.items():
                current_allocation = allocation_report['allocation_percentages'].get(asset, 0)
                allocation_diff = target_allocation - current_allocation
                
                if abs(allocation_diff) > self.rebalance_threshold:
                    target_value = total_value * Decimal(str(target_allocation))
                    current_value = Decimal(str(allocation_report['assets'].get(asset, {}).get('value', 0)))
                    trade_value = target_value - current_value
                    
                    rebalance_trades.append({
                        'asset': asset,
                        'current_allocation': current_allocation,
                        'target_allocation': target_allocation,
                        'trade_value': float(trade_value),
                        'action': 'buy' if trade_value > 0 else 'sell'
                    })
            
            return rebalance_trades
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error calculating rebalance trades: {e}")
            return []
    
    async def optimize_portfolio(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Optimize portfolio allocation based on market data"""
        try:
            # Simplified optimization - equal weight with momentum bias
            assets = list(self.portfolio.keys())
            
            if not assets:
                return {}
            
            # Equal weight baseline
            equal_weight = 1.0 / len(assets)
            optimized_allocation = {}
            
            for asset in assets:
                # Start with equal weight
                allocation = equal_weight
                
                # Adjust based on momentum (simplified)
                momentum = market_data.get(asset, {}).get('momentum', 0)
                if momentum > 0:
                    allocation *= 1.1  # Increase allocation for positive momentum
                elif momentum < 0:
                    allocation *= 0.9  # Decrease allocation for negative momentum
                
                optimized_allocation[asset] = allocation
            
            # Normalize to sum to 1.0
            total_allocation = sum(optimized_allocation.values())
            if total_allocation > 0:
                for asset in optimized_allocation:
                    optimized_allocation[asset] /= total_allocation
            
            return optimized_allocation
            
        except Exception as e:
            logger.error(f"❌ [PORTFOLIO] Error optimizing portfolio: {e}")
            return {}
