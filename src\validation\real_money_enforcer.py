#!/usr/bin/env python3
"""
Real Money Trading Enforcer - Ensures ONLY live trading operations
Validates all trading operations use production endpoints and real account balances
"""

import os
import logging
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

logger = logging.getLogger(__name__)

@dataclass
class TradingValidationResult:
    """Result of trading operation validation"""
    is_valid: bool
    validation_type: str
    exchange: str
    details: Dict[str, Any]
    timestamp: datetime
    error_message: Optional[str] = None

class RealMoneyTradingEnforcer:
    """Enforces real money trading requirements across all operations"""
    
    def __init__(self):
        self.validation_history: List[TradingValidationResult] = []
        self.failed_validations: List[TradingValidationResult] = []
        self.enforcement_active = True
        
        # Production API endpoints
        self.production_endpoints = {
            'bybit': {
                'api_url': 'https://api.bybit.com',
                'websocket_url': 'wss://stream.bybit.com',
                'required_headers': ['X-BAPI-API-KEY', 'X-BAPI-SIGN', 'X-BAPI-TIMESTAMP']
            },
            'coinbase': {
                'api_url': 'https://api.coinbase.com',
                'advanced_api_url': 'https://api.exchange.coinbase.com',
                'required_headers': ['CB-ACCESS-KEY', 'CB-ACCESS-SIGN', 'CB-ACCESS-TIMESTAMP']
            },
            'binance': {
                'api_url': 'https://api.binance.com',
                'websocket_url': 'wss://stream.binance.com',
                'required_headers': ['X-MBX-APIKEY']
            }
        }
        
        # Prohibited test/sandbox endpoints
        self.prohibited_endpoints = [
            'testnet.bybit.com',
            'sandbox.coinbase.com',
            'testnet.binance.com',
            'api.sandbox.',
            'test.api.',
            'demo.api.',
            'staging.api.',
            'localhost',
            '127.0.0.1'
        ]

    async def validate_trading_operation(self, operation_type: str, exchange: str, 
                                       operation_data: Dict[str, Any]) -> TradingValidationResult:
        """Validate a trading operation for real money compliance"""
        logger.info(f"🔍 [ENFORCE] Validating {operation_type} on {exchange}")
        
        try:
            # 1. Validate API endpoint
            endpoint_validation = await self._validate_api_endpoint(exchange, operation_data)
            if not endpoint_validation.is_valid:
                return endpoint_validation
            
            # 2. Validate account balance is real
            balance_validation = await self._validate_real_balance(exchange, operation_data)
            if not balance_validation.is_valid:
                return balance_validation
            
            # 3. Validate order parameters are for real trading
            order_validation = await self._validate_order_parameters(exchange, operation_data)
            if not order_validation.is_valid:
                return order_validation
            
            # 4. Validate no simulation flags
            simulation_validation = await self._validate_no_simulation_flags(operation_data)
            if not simulation_validation.is_valid:
                return simulation_validation
            
            # All validations passed
            result = TradingValidationResult(
                is_valid=True,
                validation_type=operation_type,
                exchange=exchange,
                details={
                    'endpoint_valid': True,
                    'balance_real': True,
                    'order_valid': True,
                    'no_simulation': True,
                    'validation_timestamp': datetime.now().isoformat()
                },
                timestamp=datetime.now()
            )
            
            self.validation_history.append(result)
            logger.info(f"✅ [ENFORCE] {operation_type} validation passed for {exchange}")
            return result
            
        except Exception as e:
            error_result = TradingValidationResult(
                is_valid=False,
                validation_type=operation_type,
                exchange=exchange,
                details={'error': str(e)},
                timestamp=datetime.now(),
                error_message=f"Validation failed: {e}"
            )
            
            self.failed_validations.append(error_result)
            logger.error(f"❌ [ENFORCE] {operation_type} validation failed for {exchange}: {e}")
            return error_result

    async def _validate_api_endpoint(self, exchange: str, operation_data: Dict[str, Any]) -> TradingValidationResult:
        """Validate that only production API endpoints are used"""
        try:
            # Check if endpoint information is available
            api_url = operation_data.get('api_url', '')
            websocket_url = operation_data.get('websocket_url', '')
            
            # If no URL provided, try to get from exchange client
            if not api_url and 'client' in operation_data:
                client = operation_data['client']
                if hasattr(client, 'api_url'):
                    api_url = client.api_url
                elif hasattr(client, 'base_url'):
                    api_url = client.base_url
            
            # Check for prohibited endpoints
            urls_to_check = [api_url, websocket_url]
            for url in urls_to_check:
                if url:
                    for prohibited in self.prohibited_endpoints:
                        if prohibited in url.lower():
                            return TradingValidationResult(
                                is_valid=False,
                                validation_type='endpoint_validation',
                                exchange=exchange,
                                details={'prohibited_endpoint': url, 'prohibited_pattern': prohibited},
                                timestamp=datetime.now(),
                                error_message=f"Prohibited endpoint detected: {url}"
                            )
            
            # Validate production endpoint
            if exchange in self.production_endpoints:
                expected_endpoints = self.production_endpoints[exchange]
                
                if api_url and not any(prod_url in api_url for prod_url in expected_endpoints.values() if isinstance(prod_url, str)):
                    return TradingValidationResult(
                        is_valid=False,
                        validation_type='endpoint_validation',
                        exchange=exchange,
                        details={'invalid_endpoint': api_url, 'expected': expected_endpoints},
                        timestamp=datetime.now(),
                        error_message=f"Invalid API endpoint for {exchange}: {api_url}"
                    )
            
            return TradingValidationResult(
                is_valid=True,
                validation_type='endpoint_validation',
                exchange=exchange,
                details={'api_url': api_url, 'websocket_url': websocket_url},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return TradingValidationResult(
                is_valid=False,
                validation_type='endpoint_validation',
                exchange=exchange,
                details={'error': str(e)},
                timestamp=datetime.now(),
                error_message=f"Endpoint validation error: {e}"
            )

    async def _validate_real_balance(self, exchange: str, operation_data: Dict[str, Any]) -> TradingValidationResult:
        """Validate that account balance is real and not mocked"""
        try:
            # Get balance information
            balance_data = operation_data.get('balance_data', {})
            
            # Check for mock balance indicators
            mock_indicators = [
                'mock', 'fake', 'test', 'dummy', 'placeholder', 
                'hardcoded', 'fallback', 'cached', 'synthetic'
            ]
            
            balance_str = str(balance_data).lower()
            for indicator in mock_indicators:
                if indicator in balance_str:
                    return TradingValidationResult(
                        is_valid=False,
                        validation_type='balance_validation',
                        exchange=exchange,
                        details={'mock_indicator': indicator, 'balance_data': balance_data},
                        timestamp=datetime.now(),
                        error_message=f"Mock balance indicator detected: {indicator}"
                    )
            
            # Validate balance has realistic precision and values
            if isinstance(balance_data, dict):
                for currency, amount in balance_data.items():
                    if isinstance(amount, (int, float, Decimal)):
                        # Check for suspiciously round numbers (often indicates mock data)
                        if float(amount) > 0 and float(amount) % 1000 == 0 and float(amount) >= 10000:
                            logger.warning(f"⚠️ [ENFORCE] Suspiciously round balance: {currency}={amount}")
            
            # Validate balance timestamp is recent (within last 5 minutes)
            balance_timestamp = operation_data.get('balance_timestamp')
            if balance_timestamp:
                if isinstance(balance_timestamp, (int, float)):
                    balance_age = time.time() - balance_timestamp
                    if balance_age > 300:  # 5 minutes
                        return TradingValidationResult(
                            is_valid=False,
                            validation_type='balance_validation',
                            exchange=exchange,
                            details={'balance_age_seconds': balance_age, 'max_age': 300},
                            timestamp=datetime.now(),
                            error_message=f"Balance data too old: {balance_age:.1f}s"
                        )
            
            return TradingValidationResult(
                is_valid=True,
                validation_type='balance_validation',
                exchange=exchange,
                details={'balance_validated': True, 'balance_data': balance_data},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return TradingValidationResult(
                is_valid=False,
                validation_type='balance_validation',
                exchange=exchange,
                details={'error': str(e)},
                timestamp=datetime.now(),
                error_message=f"Balance validation error: {e}"
            )

    async def _validate_order_parameters(self, exchange: str, operation_data: Dict[str, Any]) -> TradingValidationResult:
        """Validate order parameters are for real trading"""
        try:
            order_data = operation_data.get('order_data', {})
            
            # Check for test order indicators
            test_indicators = ['test', 'demo', 'mock', 'fake', 'simulation']
            
            # Check order ID for test patterns
            order_id = order_data.get('order_id', '')
            if order_id:
                for indicator in test_indicators:
                    if indicator in str(order_id).lower():
                        return TradingValidationResult(
                            is_valid=False,
                            validation_type='order_validation',
                            exchange=exchange,
                            details={'test_order_id': order_id},
                            timestamp=datetime.now(),
                            error_message=f"Test order ID detected: {order_id}"
                        )
            
            # Validate order amount is realistic (not obviously fake)
            order_amount = order_data.get('amount', 0)
            if order_amount:
                # Check for suspiciously precise amounts (often indicates test data)
                if isinstance(order_amount, (int, float, Decimal)):
                    amount_str = str(float(order_amount))
                    if '.' in amount_str:
                        decimal_places = len(amount_str.split('.')[1])
                        if decimal_places > 8:  # More than 8 decimal places is suspicious
                            logger.warning(f"⚠️ [ENFORCE] Suspiciously precise order amount: {order_amount}")
            
            # Validate symbol is real trading pair
            symbol = order_data.get('symbol', '')
            if symbol:
                test_symbols = ['TEST', 'DEMO', 'MOCK', 'FAKE']
                if any(test_sym in symbol.upper() for test_sym in test_symbols):
                    return TradingValidationResult(
                        is_valid=False,
                        validation_type='order_validation',
                        exchange=exchange,
                        details={'test_symbol': symbol},
                        timestamp=datetime.now(),
                        error_message=f"Test symbol detected: {symbol}"
                    )
            
            return TradingValidationResult(
                is_valid=True,
                validation_type='order_validation',
                exchange=exchange,
                details={'order_validated': True, 'order_data': order_data},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return TradingValidationResult(
                is_valid=False,
                validation_type='order_validation',
                exchange=exchange,
                details={'error': str(e)},
                timestamp=datetime.now(),
                error_message=f"Order validation error: {e}"
            )

    async def _validate_no_simulation_flags(self, operation_data: Dict[str, Any]) -> TradingValidationResult:
        """Validate no simulation flags are present"""
        try:
            # Check for simulation flags in operation data
            simulation_flags = [
                'simulation', 'test_mode', 'demo_mode', 'mock_mode',
                'paper_trading', 'dry_run', 'sandbox', 'fake_trading'
            ]
            
            data_str = str(operation_data).lower()
            for flag in simulation_flags:
                if flag in data_str:
                    # Check if it's actually enabled
                    if any(enabled in data_str for enabled in ['true', '1', 'yes', 'on', 'enabled']):
                        return TradingValidationResult(
                            is_valid=False,
                            validation_type='simulation_validation',
                            exchange='',
                            details={'simulation_flag': flag},
                            timestamp=datetime.now(),
                            error_message=f"Simulation flag detected: {flag}"
                        )
            
            # Check environment variables
            for flag in simulation_flags:
                env_value = os.getenv(flag.upper())
                if env_value and env_value.lower() in ['true', '1', 'yes', 'on']:
                    return TradingValidationResult(
                        is_valid=False,
                        validation_type='simulation_validation',
                        exchange='',
                        details={'env_simulation_flag': flag.upper(), 'value': env_value},
                        timestamp=datetime.now(),
                        error_message=f"Simulation environment variable: {flag.upper()}={env_value}"
                    )
            
            return TradingValidationResult(
                is_valid=True,
                validation_type='simulation_validation',
                exchange='',
                details={'no_simulation_flags': True},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return TradingValidationResult(
                is_valid=False,
                validation_type='simulation_validation',
                exchange='',
                details={'error': str(e)},
                timestamp=datetime.now(),
                error_message=f"Simulation validation error: {e}"
            )

    async def validate(self) -> bool:
        """
        Main validation method called by the trading system
        Performs comprehensive real money trading validation
        """
        try:
            logger.info("🔍 [ENFORCE] Starting comprehensive real money trading validation...")

            # 1. Validate environment configuration
            env_validation = await self._validate_environment_configuration()
            if not env_validation:
                logger.error("❌ [ENFORCE] Environment validation failed")
                return False

            # 2. Validate API endpoints are production
            endpoint_validation = await self._validate_production_endpoints()
            if not endpoint_validation:
                logger.error("❌ [ENFORCE] Production endpoint validation failed")
                return False

            # 3. Validate no simulation flags are present
            simulation_validation = await self._validate_no_simulation_environment()
            if not simulation_validation:
                logger.error("❌ [ENFORCE] Simulation environment validation failed")
                return False

            # 4. Validate real money trading mode is enforced
            trading_mode_validation = await self._validate_trading_mode()
            if not trading_mode_validation:
                logger.error("❌ [ENFORCE] Trading mode validation failed")
                return False

            logger.info("✅ [ENFORCE] All real money trading validations passed")
            return True

        except Exception as e:
            logger.error(f"❌ [ENFORCE] Validation error: {e}")
            return False

    async def _validate_environment_configuration(self) -> bool:
        """Validate environment is configured for real money trading"""
        try:
            # Check for prohibited environment variables
            prohibited_env_vars = {
                'DEMO_MODE': ['true', '1', 'yes', 'on'],
                'TEST_MODE': ['true', '1', 'yes', 'on'],
                'SIMULATION_MODE': ['true', '1', 'yes', 'on'],
                'PAPER_TRADING': ['true', '1', 'yes', 'on'],
                'DRY_RUN': ['true', '1', 'yes', 'on'],
                'SANDBOX_MODE': ['true', '1', 'yes', 'on']
            }

            for env_var, prohibited_values in prohibited_env_vars.items():
                env_value = os.getenv(env_var, '').lower()
                if env_value in prohibited_values:
                    logger.error(f"❌ [ENFORCE] Prohibited environment variable: {env_var}={env_value}")
                    return False

            # Check for required real money environment variables
            required_env_vars = {
                'LIVE_TRADING': ['true', '1', 'yes', 'on'],
                'REAL_MONEY_TRADING': ['true', '1', 'yes', 'on']
            }

            for env_var, required_values in required_env_vars.items():
                env_value = os.getenv(env_var, '').lower()
                if env_value not in required_values:
                    logger.warning(f"⚠️ [ENFORCE] Missing real money environment variable: {env_var}")
                    # Set it to enforce real money mode
                    os.environ[env_var] = 'true'

            return True

        except Exception as e:
            logger.error(f"❌ [ENFORCE] Environment validation error: {e}")
            return False

    async def _validate_production_endpoints(self) -> bool:
        """Validate that only production endpoints are configured"""
        try:
            # Check for prohibited test endpoints in environment
            for endpoint in self.prohibited_endpoints:
                # Check environment variables for prohibited endpoints
                for env_var in os.environ:
                    if 'URL' in env_var.upper() or 'ENDPOINT' in env_var.upper():
                        env_value = os.getenv(env_var, '').lower()
                        if endpoint in env_value:
                            logger.error(f"❌ [ENFORCE] Prohibited endpoint in {env_var}: {env_value}")
                            return False

            return True

        except Exception as e:
            logger.error(f"❌ [ENFORCE] Production endpoint validation error: {e}")
            return False

    async def _validate_no_simulation_environment(self) -> bool:
        """Validate no simulation flags are present in environment"""
        try:
            simulation_indicators = [
                'test', 'demo', 'mock', 'fake', 'simulation', 'sandbox',
                'paper', 'dry_run', 'testnet', 'staging'
            ]

            # Check environment variables
            for env_var, env_value in os.environ.items():
                env_value_lower = env_value.lower()
                for indicator in simulation_indicators:
                    if indicator in env_value_lower and any(enabled in env_value_lower for enabled in ['true', '1', 'yes', 'on']):
                        logger.error(f"❌ [ENFORCE] Simulation indicator in {env_var}: {env_value}")
                        return False

            return True

        except Exception as e:
            logger.error(f"❌ [ENFORCE] Simulation environment validation error: {e}")
            return False

    async def _validate_trading_mode(self) -> bool:
        """Validate trading mode is set to real money"""
        try:
            # Ensure enforcement is active
            if not self.enforcement_active:
                logger.error("❌ [ENFORCE] Real money enforcement is not active")
                return False

            # Check that we have production endpoints configured
            production_configured = False
            for exchange, config in self.production_endpoints.items():
                if config.get('api_url'):
                    production_configured = True
                    break

            if not production_configured:
                logger.error("❌ [ENFORCE] No production endpoints configured")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ [ENFORCE] Trading mode validation error: {e}")
            return False

    def enforce_fail_fast(self, validation_result: TradingValidationResult):
        """Enforce fail-fast behavior when validation fails"""
        if not validation_result.is_valid:
            error_msg = (
                f"CRITICAL: Real money trading validation FAILED\n"
                f"Type: {validation_result.validation_type}\n"
                f"Exchange: {validation_result.exchange}\n"
                f"Error: {validation_result.error_message}\n"
                f"Details: {validation_result.details}\n"
                f"System will terminate to prevent non-live trading operations."
            )

            logger.error(f"❌ [ENFORCE] {error_msg}")

            # Log to audit trail
            self.failed_validations.append(validation_result)

            # Terminate system
            raise RuntimeError(error_msg)

    def get_enforcement_summary(self) -> Dict[str, Any]:
        """Get summary of enforcement activities"""
        total_validations = len(self.validation_history)
        failed_validations = len(self.failed_validations)
        success_rate = ((total_validations - failed_validations) / total_validations * 100) if total_validations > 0 else 0
        
        return {
            'enforcement_active': self.enforcement_active,
            'total_validations': total_validations,
            'successful_validations': total_validations - failed_validations,
            'failed_validations': failed_validations,
            'success_rate_percent': success_rate,
            'last_validation': self.validation_history[-1].timestamp if self.validation_history else None,
            'recent_failures': [v.error_message for v in self.failed_validations[-5:]]
        }

# Global enforcer instance
real_money_enforcer = RealMoneyTradingEnforcer()

# Alias for backward compatibility
RealMoneyEnforcer = RealMoneyTradingEnforcer
