"""
Risk Manager for AutoGPT Trader
Manages trading risk and position sizing
"""

import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone
import math

logger = logging.getLogger(__name__)

class RiskManager:
    """
    Advanced risk management system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.risk_limits = {
            'max_position_size': 0.1,      # 10% max per position
            'max_daily_loss': 0.05,        # 5% max daily loss
            'max_drawdown': 0.15,          # 15% max drawdown
            'max_leverage': 3.0,           # 3x max leverage
            'var_limit': 0.02              # 2% Value at Risk limit
        }
        self.daily_pnl = Decimal('0.0')
        self.max_drawdown_reached = Decimal('0.0')
        self.risk_events = []
        
        logger.info("🛡️ [RISK] Risk management system initialized")
    
    async def calculate_position_size(self, 
                                    symbol: str, 
                                    confidence: float, 
                                    available_balance: float,
                                    volatility: float = 0.02) -> float:
        """Calculate optimal position size based on risk parameters"""
        try:
            # Base position size for aggressive micro-trading (80-90% of balance)
            base_size = available_balance * 0.85
            
            # Confidence adjustment (minimum 60% confidence required)
            if confidence < 0.60:
                return 0.0
            
            # Scale by confidence
            confidence_multiplier = min(confidence * 1.2, 1.0)
            position_size = base_size * confidence_multiplier
            
            # Volatility adjustment
            volatility_multiplier = max(0.5, 1.0 - volatility)  # Reduce size for high volatility
            position_size *= volatility_multiplier
            
            # Apply risk limits
            max_position = available_balance * self.risk_limits['max_position_size']
            position_size = min(position_size, max_position)
            
            # Ensure minimum position size for micro-trading
            if position_size < 0.90:  # $0.90 USDT minimum
                return 0.0
            
            logger.debug(f"🛡️ [RISK] Position size for {symbol}: {position_size:.4f} (confidence: {confidence:.3f})")
            return position_size
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error calculating position size: {e}")
            return 0.0
    
    async def check_risk_limits(self, proposed_trade: Dict[str, Any]) -> Dict[str, Any]:
        """Check if proposed trade violates risk limits"""
        try:
            risk_check = {
                'approved': True,
                'violations': [],
                'warnings': [],
                'adjusted_size': proposed_trade.get('position_size', 0.0)
            }
            
            position_size = proposed_trade.get('position_size', 0.0)
            available_balance = proposed_trade.get('available_balance', 0.0)
            
            # 1. Position size limit check
            if available_balance > 0:
                position_ratio = position_size / available_balance
                if position_ratio > self.risk_limits['max_position_size']:
                    risk_check['violations'].append(f"Position size exceeds limit: {position_ratio:.2%}")
                    risk_check['adjusted_size'] = available_balance * self.risk_limits['max_position_size']
                    risk_check['approved'] = False
            
            # 2. Daily loss limit check
            if self.daily_pnl < 0:
                daily_loss_ratio = abs(float(self.daily_pnl)) / max(available_balance, 1.0)
                if daily_loss_ratio > self.risk_limits['max_daily_loss']:
                    risk_check['violations'].append(f"Daily loss limit exceeded: {daily_loss_ratio:.2%}")
                    risk_check['approved'] = False
            
            # 3. Drawdown check
            if self.max_drawdown_reached > 0:
                drawdown_ratio = float(self.max_drawdown_reached) / max(available_balance, 1.0)
                if drawdown_ratio > self.risk_limits['max_drawdown']:
                    risk_check['violations'].append(f"Max drawdown exceeded: {drawdown_ratio:.2%}")
                    risk_check['approved'] = False
            
            # 4. Minimum position size check
            if position_size < 0.90:
                risk_check['warnings'].append("Position size below minimum threshold")
                risk_check['approved'] = False
            
            return risk_check
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error checking risk limits: {e}")
            return {'approved': False, 'violations': [str(e)], 'warnings': [], 'adjusted_size': 0.0}
    
    async def update_pnl(self, trade_result: Dict[str, Any]) -> None:
        """Update P&L tracking"""
        try:
            profit = Decimal(str(trade_result.get('profit', 0.0)))
            self.daily_pnl += profit
            
            # Update drawdown tracking
            if profit < 0:
                self.max_drawdown_reached = max(self.max_drawdown_reached, abs(profit))
            
            # Log risk events
            if profit < 0 and abs(profit) > 10.0:  # Significant loss
                self.risk_events.append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'event_type': 'significant_loss',
                    'amount': float(profit),
                    'symbol': trade_result.get('symbol', 'UNKNOWN')
                })
            
            logger.debug(f"🛡️ [RISK] P&L updated: {profit}, daily total: {self.daily_pnl}")
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error updating P&L: {e}")
    
    async def calculate_var(self, portfolio_value: float, confidence_level: float = 0.95) -> float:
        """Calculate Value at Risk"""
        try:
            # Simplified VaR calculation
            # In practice, this would use historical returns and statistical models
            
            # Assume 2% daily volatility
            daily_volatility = 0.02
            
            # Calculate VaR using normal distribution approximation
            from scipy.stats import norm
            z_score = norm.ppf(1 - confidence_level)
            var = portfolio_value * daily_volatility * abs(z_score)
            
            return var
            
        except ImportError:
            # Fallback calculation without scipy
            # Use simplified approximation: 2.33 * volatility for 99% confidence
            z_score = 2.33 if confidence_level >= 0.99 else 1.65
            var = portfolio_value * 0.02 * z_score
            return var
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error calculating VaR: {e}")
            return 0.0
    
    async def get_risk_report(self) -> Dict[str, Any]:
        """Get comprehensive risk report"""
        try:
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'daily_pnl': float(self.daily_pnl),
                'max_drawdown': float(self.max_drawdown_reached),
                'risk_limits': self.risk_limits,
                'risk_events_count': len(self.risk_events),
                'recent_risk_events': self.risk_events[-5:],  # Last 5 events
                'risk_status': 'healthy' if self.daily_pnl >= 0 else 'monitoring'
            }
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error generating risk report: {e}")
            return {'error': str(e)}
    
    async def reset_daily_metrics(self) -> None:
        """Reset daily risk metrics"""
        try:
            self.daily_pnl = Decimal('0.0')
            logger.info("🛡️ [RISK] Daily risk metrics reset")
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error resetting daily metrics: {e}")
    
    async def adjust_risk_limits(self, market_conditions: Dict[str, Any]) -> None:
        """Dynamically adjust risk limits based on market conditions"""
        try:
            volatility = market_conditions.get('volatility', 0.02)
            
            # Adjust position size limit based on volatility
            if volatility > 0.05:  # High volatility
                self.risk_limits['max_position_size'] = 0.05  # Reduce to 5%
            elif volatility < 0.01:  # Low volatility
                self.risk_limits['max_position_size'] = 0.15  # Increase to 15%
            else:
                self.risk_limits['max_position_size'] = 0.1   # Default 10%
            
            logger.debug(f"🛡️ [RISK] Risk limits adjusted for volatility: {volatility:.3f}")
            
        except Exception as e:
            logger.error(f"❌ [RISK] Error adjusting risk limits: {e}")
