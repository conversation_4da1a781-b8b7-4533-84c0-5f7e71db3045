"""
Enhanced Profit Prediction System for AutoGPT-Trader
Advanced ML-based profit forecasting with ensemble methods and real-time optimization
"""

import logging
import numpy as np
import asyncio

# Initialize logger first
logger = logging.getLogger(__name__)

# Optional pandas import with fallback
try:
    import pandas as pd
except ImportError:
    # Create mock pandas for fallback
    class MockPandas:
        def DataFrame(self, data=None):
            return data or {}
    pd = MockPandas()
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from decimal import Decimal

# ML imports with proper error handling
try:
    import tensorflow as tf
    # Fix TensorFlow deprecation warnings - handle different TF versions
    try:
        if hasattr(tf, 'compat') and hasattr(tf.compat, 'v1'):
            tf.compat.v1.logging.set_verbosity(tf.compat.v1.logging.ERROR)
    except (AttributeError, ImportError):
        pass  # Skip if TensorFlow version doesn't support compat.v1
    import warnings
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    TF_AVAILABLE = True
    logger.info("✅ [TENSORFLOW] TensorFlow imported successfully")
except ImportError:
    TF_AVAILABLE = False
    logger.warning("⚠️ [TENSORFLOW] TensorFlow not available, using alternative implementation")
    try:
        from .tensorflow_alternative import tf_alternative as tf
        from .tensorflow_alternative import enable_deterministic_ops, keras_Sequential, compile_model, fit_model, predict
        TF_AVAILABLE = True  # Alternative is available
        logger.info("✅ [TENSORFLOW-ALT] TensorFlow alternative loaded successfully")
    except ImportError as e:
        logger.error(f"❌ [TENSORFLOW-ALT] Failed to load TensorFlow alternative: {e}")
        TF_AVAILABLE = False

# Additional ML imports
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import mean_absolute_error, r2_score
    import xgboost as xgb
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class ProfitPrediction:
    """Profit prediction result"""
    symbol: str
    predicted_profit: float  # Expected profit/loss percentage
    profit_probability: float  # Probability of profit (0-1)
    optimal_entry_price: float
    optimal_exit_price: float
    time_to_target: int  # Minutes to reach target
    confidence: float
    risk_adjusted_profit: float
    max_drawdown_expected: float
    profit_factors: Dict[str, float]
    strategy_recommendations: List[str]
    timestamp: datetime

class EnhancedProfitPredictor:
    """
    Advanced profit prediction system using ensemble ML methods
    Optimizes for maximum profit with minimum risk and time
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize enhanced profit predictor"""
        self.config = config or self._default_config()
        self.models = {}
        self.scalers = {}
        self.profit_history = {}
        self.is_trained = False
        
        # Profit optimization parameters
        self.profit_targets = {
            'conservative': 0.02,  # 2%
            'moderate': 0.05,      # 5%
            'aggressive': 0.10     # 10%
        }
        
        # Time horizons for profit prediction
        self.time_horizons = ['15m', '1h', '4h', '1d']
        
        # Feature importance tracking
        self.feature_importance = {}

        # Real-time profit tracking enhancements
        self.profit_cache = {}
        self.cache_ttl = 45  # 45 seconds cache for profit predictions
        self.last_cache_update = {}

        # Time-based profit optimization
        self.time_profit_models = {}
        self.optimal_timing_cache = {}

        # Profit prediction accuracy tracking
        self.prediction_accuracy = {
            'total_predictions': 0,
            'accurate_predictions': 0,
            'accuracy_rate': 0.0,
            'profit_realized': 0.0,
            'profit_predicted': 0.0,
            'last_updated': datetime.now(timezone.utc)
        }

        # Enhanced profit optimization parameters
        self.profit_optimization_weights = {
            'time_factor': 0.3,      # Weight for time optimization
            'risk_factor': 0.25,     # Weight for risk adjustment
            'sentiment_factor': 0.2,  # Weight for sentiment boost
            'technical_factor': 0.15, # Weight for technical indicators
            'momentum_factor': 0.1   # Weight for momentum signals
        }

        # Initialize models if ML is available
        if ML_AVAILABLE:
            self._initialize_models()
        else:
            logger.warning("ML libraries not available, using simplified profit prediction")

        logger.info("Enhanced Profit Predictor initialized with real-time optimization and time-based analysis")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration for profit prediction"""
        return {
            'lookback_window': 200,
            'prediction_horizons': ['15m', '1h', '4h', '1d'],
            'profit_factors': [
                'price_momentum', 'volume_trend', 'volatility', 'sentiment',
                'technical_indicators', 'market_structure', 'liquidity'
            ],
            'model_types': ['random_forest', 'xgboost', 'gradient_boost', 'lstm'],
            'ensemble_weights': {
                'random_forest': 0.25,
                'xgboost': 0.35,
                'gradient_boost': 0.25,
                'lstm': 0.15
            },
            'retraining_interval': 1800,  # 30 minutes
            'confidence_threshold': 0.6,
            'profit_threshold': 0.01,  # Minimum 1% profit target
            'risk_adjustment': True,
            'time_optimization': True
        }
    
    def _initialize_models(self):
        """Initialize ML models for profit prediction"""
        try:
            # Random Forest for robust predictions
            self.models['random_forest'] = RandomForestRegressor(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            # XGBoost for high-performance predictions
            self.models['xgboost'] = xgb.XGBRegressor(
                n_estimators=500,
                max_depth=10,
                learning_rate=0.01,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
            
            # Gradient Boosting for ensemble diversity
            self.models['gradient_boost'] = GradientBoostingRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.05,
                subsample=0.8,
                random_state=42
            )
            
            # LSTM for time series profit prediction
            if ML_AVAILABLE:
                self.models['lstm'] = self._create_lstm_profit_model()
            
            # Initialize scalers
            self.scalers['standard'] = StandardScaler()
            self.scalers['minmax'] = MinMaxScaler()
            
            logger.info("Profit prediction models initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing profit models: {e}")
            self.models = {}
    
    def _create_lstm_profit_model(self):
        """Create LSTM model for profit prediction with standardized input dimensions"""
        try:
            if not TF_AVAILABLE:
                logger.warning("TensorFlow not available - using fallback profit prediction model")
                return None

            # Use standardized feature count (22 features to match actual extraction)
            expected_features = 22  # Based on _extract_profit_features method

            model = tf.keras.Sequential([
                tf.keras.layers.LSTM(128, return_sequences=True, input_shape=(60, expected_features)),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.LSTM(64, return_sequences=True),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.LSTM(32, return_sequences=False),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.Dense(32, activation='relu'),
                tf.keras.layers.Dense(16, activation='relu'),
                tf.keras.layers.Dense(3)  # [profit_pct, probability, time_to_target]
            ])

            model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )

            logger.info(f"LSTM profit model created with input shape: (60, {expected_features})")
            return model

        except Exception as e:
            logger.error(f"Error creating LSTM profit model: {e}")
            return None
    
    async def predict_profit(self, market_data: Dict[str, Any], symbol: str,
                           sentiment_data: Optional[Dict[str, Any]] = None,
                           risk_data: Optional[Dict[str, Any]] = None) -> ProfitPrediction:
        """
        Predict comprehensive profit potential with real-time caching and time optimization

        Args:
            market_data: Real-time market data
            symbol: Trading symbol
            sentiment_data: Optional sentiment analysis data
            risk_data: Optional risk assessment data

        Returns:
            ProfitPrediction with comprehensive profit forecast optimized for time and risk
        """
        try:
            # Check cache first for performance
            cache_key = f"{symbol}_profit"
            now = datetime.now(timezone.utc)

            if (cache_key in self.profit_cache and
                cache_key in self.last_cache_update and
                (now - self.last_cache_update[cache_key]).total_seconds() < self.cache_ttl):
                cached_prediction = self.profit_cache[cache_key]
                logger.debug(f"Using cached profit prediction for {symbol}")
                return cached_prediction

            # Extract features for profit prediction
            features = await self._extract_profit_features(market_data, symbol, sentiment_data, risk_data)

            if not features:
                return self._fallback_profit_prediction(symbol)

            # Time-based feature enhancement
            time_enhanced_features = self._enhance_features_with_timing(features, market_data)

            # Get predictions from all available models
            profit_predictions = {}
            confidences = {}

            if ML_AVAILABLE and self.models:
                for model_name, model in self.models.items():
                    try:
                        if model_name in ['random_forest', 'xgboost', 'gradient_boost']:
                            # Regression-based profit prediction
                            profit_pred = self._predict_regression_profit(time_enhanced_features, model)
                            profit_predictions[model_name] = profit_pred
                            confidences[model_name] = 0.9

                        elif model_name == 'lstm' and model is not None:
                            # Time series profit prediction
                            lstm_profit = await self._predict_lstm_profit(time_enhanced_features, model)
                            profit_predictions[model_name] = lstm_profit
                            confidences[model_name] = 0.85

                    except Exception as e:
                        logger.error(f"Error with {model_name} profit prediction: {e}")
                        continue

            # Ensemble profit prediction with time optimization
            if profit_predictions:
                final_profit = self._ensemble_profit_prediction(profit_predictions, confidences)
                overall_confidence = np.mean(list(confidences.values()))
            else:
                # Fallback to rule-based profit assessment
                final_profit = self._calculate_rule_based_profit(time_enhanced_features)
                overall_confidence = 0.6

            # Apply time-based profit optimization
            time_optimized_profit = self._apply_time_optimization(final_profit, time_enhanced_features)

            # Calculate optimal entry/exit prices with timing
            current_price = time_enhanced_features.get('current_price', 0.0)
            optimal_entry, optimal_exit = self._calculate_optimal_prices_with_timing(
                current_price, time_optimized_profit, time_enhanced_features
            )

            # Enhanced time to target calculation
            time_to_target = self._estimate_optimal_time_to_target(time_enhanced_features, time_optimized_profit)

            # Risk-adjusted profit calculation with time factor
            risk_score = risk_data.get('risk_score', 0.5) if risk_data else 0.5
            time_risk_factor = self._calculate_time_risk_factor(time_to_target)
            risk_adjusted_profit = time_optimized_profit * (1 - (risk_score + time_risk_factor) * 0.3)

            # Calculate enhanced profit factors
            profit_factors = self._calculate_enhanced_profit_factors(time_enhanced_features, sentiment_data)

            # Generate time-optimized strategy recommendations
            recommendations = self._generate_time_optimized_strategies(
                time_optimized_profit, risk_adjusted_profit, time_enhanced_features, sentiment_data, time_to_target
            )

            # Calculate expected drawdown with time consideration
            max_drawdown = self._estimate_time_adjusted_drawdown(time_enhanced_features, time_optimized_profit)

            # Create profit prediction
            profit_prediction = ProfitPrediction(
                symbol=symbol,
                predicted_profit=time_optimized_profit,
                profit_probability=self._calculate_time_adjusted_probability(time_optimized_profit, time_enhanced_features),
                optimal_entry_price=optimal_entry,
                optimal_exit_price=optimal_exit,
                time_to_target=time_to_target,
                confidence=overall_confidence,
                risk_adjusted_profit=risk_adjusted_profit,
                max_drawdown_expected=max_drawdown,
                profit_factors=profit_factors,
                strategy_recommendations=recommendations,
                timestamp=now
            )

            # Cache the prediction
            self.profit_cache[cache_key] = profit_prediction
            self.last_cache_update[cache_key] = now

            # Store prediction for accuracy tracking
            self._store_prediction_for_accuracy_tracking(profit_prediction, time_enhanced_features)

            return profit_prediction

        except Exception as e:
            logger.error(f"Error predicting profit for {symbol}: {e}")
            return self._fallback_profit_prediction(symbol)
    
    async def _extract_profit_features(self, market_data: Dict[str, Any], symbol: str,
                                     sentiment_data: Optional[Dict[str, Any]] = None,
                                     risk_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract features for profit prediction with Fear & Greed Index integration"""
        try:
            features = {}

            # Price-based features
            price_data = market_data.get('price_data', {})
            if price_data and symbol in price_data:
                symbol_data = price_data[symbol]

                features['current_price'] = float(symbol_data.get('price', 0))
                features['price_change_1h'] = float(symbol_data.get('price_change_1h', 0))
                features['price_change_24h'] = float(symbol_data.get('price_change_24h', 0))
                features['volume'] = float(symbol_data.get('volume', 0))
                features['volume_change'] = float(symbol_data.get('volume_change_24h', 0))
                features['volatility'] = float(symbol_data.get('volatility', 0.1))

                # Momentum indicators
                features['momentum_1h'] = features['price_change_1h'] / features['current_price'] if features['current_price'] > 0 else 0
                features['momentum_24h'] = features['price_change_24h'] / features['current_price'] if features['current_price'] > 0 else 0

            # Technical indicators (only include the 5 expected by LSTM)
            technical_data = market_data.get('technical_indicators', {})
            if technical_data:
                features['rsi'] = technical_data.get('rsi', 50.0)
                features['macd'] = technical_data.get('macd', 0.0)
                features['macd_signal'] = technical_data.get('macd_signal', 0.0)
                features['bollinger_upper'] = technical_data.get('bollinger_upper', 0.0)
                features['bollinger_lower'] = technical_data.get('bollinger_lower', 0.0)
                # Note: sma_20, ema_12, ema_26 excluded to match LSTM expected dimensions

            # Enhanced sentiment features with Fear & Greed Index
            if sentiment_data:
                features['sentiment_score'] = sentiment_data.get('aggregated_sentiment', 0.0)
                features['sentiment_confidence'] = sentiment_data.get('confidence', 0.0)
                features['sentiment_strength'] = sentiment_data.get('signal_strength', 0.0)

            # Fear & Greed Index integration from live data
            features.update(await self._extract_fear_greed_features(market_data))

            # Enhanced market sentiment from multiple sources
            features.update(await self._extract_enhanced_sentiment_features(market_data, sentiment_data))
            
            # Risk features (only include the 2 expected by LSTM)
            if risk_data:
                features['risk_score'] = risk_data.get('risk_score', 0.5)
                features['volatility_risk'] = risk_data.get('volatility_prediction', 0.1)
                # Note: liquidity_risk excluded to match LSTM expected dimensions
            
            # Market structure features (not included in LSTM - will be handled in standardization)
            features['market_cap_rank'] = self._get_market_cap_rank(symbol)
            features['trading_volume_rank'] = self._get_volume_rank(symbol, market_data)

            # Time-based features (required for LSTM)
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc)
            features['hour_of_day'] = current_time.hour / 24.0  # Normalize to 0-1
            features['day_of_week'] = current_time.weekday() / 6.0  # Normalize to 0-1

            # US market session (simplified)
            us_hour = (current_time.hour - 5) % 24  # EST adjustment
            features['us_market_session'] = 1.0 if 9 <= us_hour <= 16 else 0.0

            # Time momentum strength (based on recent price changes)
            features['time_momentum_strength'] = min(1.0, abs(features.get('momentum_1h', 0.0)) * 10)

            # Standardize features to exactly 26 dimensions for enhanced LSTM compatibility
            return self._standardize_profit_features(features)

        except Exception as e:
            logger.error(f"Error extracting profit features: {e}")
            return self._get_default_profit_features()

    async def _extract_fear_greed_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract Fear & Greed Index features for profit prediction"""
        try:
            features = {}

            # Extract Fear & Greed Index from live data aggregated
            live_aggregated = market_data.get('live_aggregated', {})

            # Current Fear & Greed Index (0-100 scale, normalize to 0-1)
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0)  # Default to current value
            features['fear_greed_index'] = fear_greed_index / 100.0

            # Fear & Greed trend analysis
            features['fear_greed_trend'] = self._calculate_fear_greed_trend(fear_greed_index)

            # Fear & Greed momentum (rate of change)
            features['fear_greed_momentum'] = self._calculate_fear_greed_momentum(fear_greed_index)

            # Market regime based on Fear & Greed
            features['market_regime_sentiment'] = self._determine_market_regime_from_fear_greed(fear_greed_index)

            logger.debug(f"Fear & Greed features extracted: {features}")
            return features

        except Exception as e:
            logger.error(f"Error extracting Fear & Greed features: {e}")
            return {
                'fear_greed_index': 0.65,  # Default neutral
                'fear_greed_trend': 0.0,
                'fear_greed_momentum': 0.0,
                'market_regime_sentiment': 0.5
            }

    async def _extract_enhanced_sentiment_features(self, market_data: Dict[str, Any],
                                                 sentiment_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Extract enhanced sentiment features from multiple data sources"""
        try:
            features = {}

            # Web crawler sentiment insights
            web_insights = market_data.get('web_crawler_insights', {})
            if web_insights:
                features['web_sentiment_score'] = web_insights.get('overall_sentiment', 0.0)
                features['web_sentiment_confidence'] = web_insights.get('confidence', 0.0)
                features['news_impact_score'] = web_insights.get('high_impact_count', 0) / 10.0  # Normalize
            else:
                features['web_sentiment_score'] = 0.0
                features['web_sentiment_confidence'] = 0.0
                features['news_impact_score'] = 0.0

            # Composite sentiment scoring
            if sentiment_data:
                base_sentiment = sentiment_data.get('aggregated_sentiment', 0.0)
                web_sentiment = features['web_sentiment_score']
                fear_greed_sentiment = (market_data.get('live_aggregated', {}).get('fear_greed_index', 65.0) - 50) / 50.0

                # Weighted composite sentiment
                features['composite_sentiment'] = (
                    base_sentiment * 0.4 +
                    web_sentiment * 0.3 +
                    fear_greed_sentiment * 0.3
                )
            else:
                features['composite_sentiment'] = 0.0

            return features

        except Exception as e:
            logger.error(f"Error extracting enhanced sentiment features: {e}")
            return {
                'web_sentiment_score': 0.0,
                'web_sentiment_confidence': 0.0,
                'news_impact_score': 0.0,
                'composite_sentiment': 0.0
            }

    def _standardize_profit_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Standardize features to exactly 30 dimensions for enhanced neural network compatibility"""
        try:
            # Define the exact 30 features expected by the enhanced LSTM model
            expected_features = [
                # Price features (6)
                'current_price', 'price_change_1h', 'price_change_24h', 'volume', 'volume_change', 'volatility',
                # Momentum features (2)
                'momentum_1h', 'momentum_24h',
                # Technical indicators (5)
                'rsi', 'macd', 'macd_signal', 'bollinger_upper', 'bollinger_lower',
                # Sentiment features (3)
                'sentiment_score', 'sentiment_confidence', 'sentiment_strength',
                # Risk features (2)
                'risk_score', 'volatility_risk',
                # Time features (4)
                'hour_of_day', 'day_of_week', 'us_market_session', 'time_momentum_strength',
                # Fear & Greed Index features (4)
                'fear_greed_index', 'fear_greed_trend', 'fear_greed_momentum', 'market_regime_sentiment',
                # Enhanced sentiment features (4)
                'web_sentiment_score', 'web_sentiment_confidence', 'news_impact_score', 'composite_sentiment'
            ]

            # Create standardized feature dict with defaults
            standardized = {}
            for feature_name in expected_features:
                if feature_name in features:
                    standardized[feature_name] = features[feature_name]
                else:
                    # Provide sensible defaults for missing features
                    defaults = {
                        'current_price': 0.0, 'price_change_1h': 0.0, 'price_change_24h': 0.0,
                        'volume': 0.0, 'volume_change': 0.0, 'volatility': 0.1,
                        'momentum_1h': 0.0, 'momentum_24h': 0.0,
                        'rsi': 50.0, 'macd': 0.0, 'macd_signal': 0.0, 'bollinger_upper': 0.0, 'bollinger_lower': 0.0,
                        'sentiment_score': 0.0, 'sentiment_confidence': 0.0, 'sentiment_strength': 0.0,
                        'risk_score': 0.5, 'volatility_risk': 0.1,
                        'hour_of_day': 0.5, 'day_of_week': 0.5, 'us_market_session': 0.0, 'time_momentum_strength': 0.5,
                        # Fear & Greed Index defaults
                        'fear_greed_index': 0.65, 'fear_greed_trend': 0.0, 'fear_greed_momentum': 0.0, 'market_regime_sentiment': 0.5,
                        # Enhanced sentiment defaults
                        'web_sentiment_score': 0.0, 'web_sentiment_confidence': 0.0, 'news_impact_score': 0.0, 'composite_sentiment': 0.0
                    }
                    standardized[feature_name] = defaults.get(feature_name, 0.0)

            return standardized

        except Exception as e:
            logger.error(f"Error standardizing profit features: {e}")
            return self._get_default_profit_features()

    def _get_default_profit_features(self) -> Dict[str, float]:
        """Get default feature set with exactly 22 features"""
        return {
            'current_price': 0.0, 'price_change_1h': 0.0, 'price_change_24h': 0.0,
            'volume': 0.0, 'volume_change': 0.0, 'volatility': 0.1,
            'momentum_1h': 0.0, 'momentum_24h': 0.0,
            'rsi': 50.0, 'macd': 0.0, 'macd_signal': 0.0, 'bollinger_upper': 0.0, 'bollinger_lower': 0.0,
            'sentiment_score': 0.0, 'sentiment_confidence': 0.0, 'sentiment_strength': 0.0,
            'risk_score': 0.5, 'volatility_risk': 0.1,
            'hour_of_day': 0.5, 'day_of_week': 0.5, 'us_market_session': 0.0, 'time_momentum_strength': 0.5
        }

    def _predict_regression_profit(self, features: Dict[str, float], model) -> float:
        """Predict profit using regression model"""
        try:
            # Check if model is fitted
            if not hasattr(model, 'feature_importances_') and not hasattr(model, 'coef_') and not hasattr(model, 'estimators_'):
                logger.debug("Model not fitted yet, using fallback prediction")
                return 0.0

            feature_array = np.array(list(features.values())).reshape(1, -1)

            # Scale features if scaler is fitted
            if 'standard' in self.scalers and hasattr(self.scalers['standard'], 'scale_'):
                feature_array = self.scalers['standard'].transform(feature_array)
            elif 'standard' in self.scalers:
                # Fit scaler with current features if not fitted
                self.scalers['standard'].fit(feature_array)
                feature_array = self.scalers['standard'].transform(feature_array)

            # Get profit prediction
            profit_pred = model.predict(feature_array)[0]

            return float(profit_pred)

        except Exception as e:
            logger.error(f"Error in regression profit prediction: {e}")
            return 0.0

    async def _predict_lstm_profit(self, features: Dict[str, float], model) -> float:
        """Predict profit using LSTM model"""
        try:
            # Check if model is properly initialized and fitted
            if not hasattr(model, 'predict') or not hasattr(model, 'layers'):
                logger.debug("LSTM model not properly initialized, using fallback prediction")
                return 0.0

            # Ensure exactly 22 features for LSTM compatibility
            standardized_features = self._standardize_profit_features(features)
            feature_array = np.array(list(standardized_features.values()))

            # Verify feature count matches LSTM input
            if len(feature_array) != 22:
                logger.warning(f"Feature count mismatch: got {len(feature_array)}, expected 22. Using fallback.")
                return 0.0

            # Create sequence (repeat current features for sequence length)
            sequence = np.tile(feature_array, (60, 1)).reshape(1, 60, 22)

            # Predict [profit_pct, probability, time_to_target]
            prediction = model.predict(sequence)[0]

            return float(prediction[0])  # Return profit percentage

        except Exception as e:
            logger.error(f"Error in LSTM profit prediction: {e}")
            return 0.0

    def _ensemble_profit_prediction(self, predictions: Dict[str, float],
                                   confidences: Dict[str, float]) -> float:
        """Combine multiple profit predictions"""
        try:
            if not predictions:
                return 0.0

            # Use configured ensemble weights
            weights = self.config.get('ensemble_weights', {})

            total_weighted_profit = 0.0
            total_weight = 0.0

            for model_name, profit in predictions.items():
                model_weight = weights.get(model_name, 0.25)
                confidence_weight = confidences.get(model_name, 0.5)
                final_weight = model_weight * confidence_weight

                total_weighted_profit += profit * final_weight
                total_weight += final_weight

            if total_weight > 0:
                return total_weighted_profit / total_weight
            else:
                return np.mean(list(predictions.values()))

        except Exception as e:
            logger.error(f"Error in ensemble profit prediction: {e}")
            return 0.0

    def _calculate_rule_based_profit(self, features: Dict[str, float]) -> float:
        """Calculate profit using rule-based approach"""
        try:
            profit_score = 0.0

            # Momentum-based profit potential
            momentum_1h = features.get('momentum_1h', 0.0)
            momentum_24h = features.get('momentum_24h', 0.0)

            # Positive momentum indicates profit potential
            if momentum_1h > 0.01:  # 1% positive momentum
                profit_score += momentum_1h * 2  # Amplify short-term momentum

            if momentum_24h > 0.02:  # 2% positive momentum
                profit_score += momentum_24h * 1.5

            # Volume-based profit potential
            volume_change = features.get('volume_change', 0.0)
            if volume_change > 0.2:  # 20% volume increase
                profit_score += 0.02  # Add 2% profit potential

            # Technical indicator signals
            rsi = features.get('rsi', 50.0)
            if rsi < 30:  # Oversold condition
                profit_score += 0.03  # Add 3% profit potential
            elif rsi > 70:  # Overbought condition
                profit_score -= 0.02  # Reduce profit potential

            # Sentiment boost
            sentiment_score = features.get('sentiment_score', 0.0)
            sentiment_confidence = features.get('sentiment_confidence', 0.0)
            if sentiment_score > 0.3 and sentiment_confidence > 0.6:
                profit_score += sentiment_score * sentiment_confidence * 0.05

            # Risk adjustment
            risk_score = features.get('risk_score', 0.5)
            profit_score *= (1 - risk_score * 0.3)  # Reduce profit by risk

            # ENHANCED HIGH PROFIT FOCUS: Increase profit targets for high-confidence signals
            if profit_score > 0.05 and sentiment_confidence > 0.8:  # High confidence signal
                profit_score *= 2.0  # Double the profit target for high-confidence opportunities

            return max(-0.1, min(0.5, profit_score))  # ENHANCED: Cap between -10% and +50% for high profits

        except Exception as e:
            logger.error(f"Error in rule-based profit calculation: {e}")
            return 0.0

    def _calculate_optimal_prices(self, current_price: float, predicted_profit: float,
                                 features: Dict[str, float]) -> Tuple[float, float]:
        """Calculate optimal entry and exit prices"""
        try:
            if current_price <= 0:
                return 0.0, 0.0

            # Volatility-adjusted entry
            volatility = features.get('volatility', 0.1)

            # For positive profit prediction, enter slightly below current price
            if predicted_profit > 0:
                entry_discount = min(0.005, volatility * 0.5)  # Max 0.5% discount
                optimal_entry = current_price * (1 - entry_discount)
                optimal_exit = current_price * (1 + predicted_profit)
            else:
                # For negative prediction, suggest higher entry (short position)
                entry_premium = min(0.005, volatility * 0.5)
                optimal_entry = current_price * (1 + entry_premium)
                optimal_exit = current_price * (1 + predicted_profit)

            return optimal_entry, optimal_exit

        except Exception as e:
            logger.error(f"Error calculating optimal prices: {e}")
            return current_price, current_price

    def _estimate_time_to_target(self, features: Dict[str, float], predicted_profit: float) -> int:
        """Estimate time to reach profit target in minutes"""
        try:
            # Base time estimation on volatility and momentum
            volatility = features.get('volatility', 0.1)
            momentum_1h = abs(features.get('momentum_1h', 0.0))

            # Higher volatility and momentum = faster target achievement
            speed_factor = (volatility + momentum_1h) / 2

            # Base time for different profit levels
            if abs(predicted_profit) < 0.01:  # < 1%
                base_time = 30  # 30 minutes
            elif abs(predicted_profit) < 0.03:  # < 3%
                base_time = 120  # 2 hours
            elif abs(predicted_profit) < 0.05:  # < 5%
                base_time = 240  # 4 hours
            else:
                base_time = 480  # 8 hours

            # Adjust by speed factor
            if speed_factor > 0:
                estimated_time = int(base_time / (1 + speed_factor * 2))
            else:
                estimated_time = base_time

            return max(15, min(1440, estimated_time))  # Between 15 minutes and 24 hours

        except Exception as e:
            logger.error(f"Error estimating time to target: {e}")
            return 120  # Default 2 hours

    def _calculate_profit_probability(self, predicted_profit: float, features: Dict[str, float]) -> float:
        """Calculate probability of achieving predicted profit"""
        try:
            # Base probability on prediction magnitude and market conditions
            base_prob = 0.5

            # Adjust for prediction confidence
            profit_magnitude = abs(predicted_profit)
            if profit_magnitude < 0.01:  # Small predictions are more likely
                base_prob += 0.2
            elif profit_magnitude > 0.05:  # Large predictions are less likely
                base_prob -= 0.2

            # Adjust for market conditions
            volatility = features.get('volatility', 0.1)
            if volatility > 0.2:  # High volatility reduces probability
                base_prob -= 0.1

            # Sentiment boost
            sentiment_confidence = features.get('sentiment_confidence', 0.0)
            base_prob += sentiment_confidence * 0.2

            # Risk adjustment
            risk_score = features.get('risk_score', 0.5)
            base_prob -= risk_score * 0.3

            return max(0.1, min(0.9, base_prob))

        except Exception as e:
            logger.error(f"Error calculating profit probability: {e}")
            return 0.5

    def _calculate_profit_factors(self, features: Dict[str, float],
                                 sentiment_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Calculate individual profit contributing factors"""
        try:
            factors = {}

            # Technical factors
            factors['momentum'] = (features.get('momentum_1h', 0.0) + features.get('momentum_24h', 0.0)) / 2
            factors['volume'] = min(1.0, features.get('volume_change', 0.0) / 0.5)  # Normalize to 50% change
            factors['volatility'] = features.get('volatility', 0.1)

            # Sentiment factors
            if sentiment_data:
                factors['sentiment'] = sentiment_data.get('aggregated_sentiment', 0.0)
                factors['sentiment_strength'] = sentiment_data.get('signal_strength', 0.0)
            else:
                factors['sentiment'] = 0.0
                factors['sentiment_strength'] = 0.0

            # Technical indicator factors
            rsi = features.get('rsi', 50.0)
            factors['rsi_signal'] = (50 - rsi) / 50  # Oversold = positive, overbought = negative

            macd = features.get('macd', 0.0)
            factors['macd_signal'] = min(1.0, max(-1.0, macd / 0.01))  # Normalize MACD

            return factors

        except Exception as e:
            logger.error(f"Error calculating profit factors: {e}")
            return {}

    def _generate_profit_strategies(self, predicted_profit: float, risk_adjusted_profit: float,
                                   features: Dict[str, float],
                                   sentiment_data: Optional[Dict[str, Any]] = None) -> List[str]:
        """Generate profit optimization strategies"""
        strategies = []

        try:
            # Strategy based on profit magnitude
            if predicted_profit > 0.05:  # > 5% profit
                strategies.append("HIGH PROFIT POTENTIAL: Use aggressive position sizing")
                strategies.append("Consider taking partial profits at 3% and 5% levels")
            elif predicted_profit > 0.02:  # > 2% profit
                strategies.append("MODERATE PROFIT: Use standard position sizing")
                strategies.append("Set profit target at predicted level with trailing stop")
            elif predicted_profit > 0:  # Positive but small
                strategies.append("SMALL PROFIT: Use conservative position sizing")
                strategies.append("Quick scalping strategy recommended")
            else:
                strategies.append("NEGATIVE PREDICTION: Avoid long positions")
                strategies.append("Consider short position if conditions allow")

            # Time-based strategies
            time_to_target = self._estimate_time_to_target(features, predicted_profit)
            if time_to_target < 60:  # < 1 hour
                strategies.append("FAST TARGET: Use market orders for quick execution")
            elif time_to_target > 240:  # > 4 hours
                strategies.append("SLOW TARGET: Use limit orders and be patient")

            # Risk-adjusted strategies
            risk_score = features.get('risk_score', 0.5)
            if risk_score > 0.7:
                strategies.append("HIGH RISK: Reduce position size by 50%")
                strategies.append("Use tight stop losses")

            # Sentiment-based strategies
            if sentiment_data:
                sentiment_score = sentiment_data.get('aggregated_sentiment', 0.0)
                if abs(sentiment_score) > 0.5:
                    strategies.append("STRONG SENTIMENT: Consider sentiment-driven timing")

            # Volatility strategies
            volatility = features.get('volatility', 0.1)
            if volatility > 0.2:
                strategies.append("HIGH VOLATILITY: Use wider stop losses")
                strategies.append("Consider options strategies for volatility play")

            return strategies

        except Exception as e:
            logger.error(f"Error generating profit strategies: {e}")
            return ["Use standard trading approach"]

    def _estimate_max_drawdown(self, features: Dict[str, float], predicted_profit: float) -> float:
        """Estimate maximum expected drawdown"""
        try:
            volatility = features.get('volatility', 0.1)
            risk_score = features.get('risk_score', 0.5)

            # Base drawdown on volatility and risk
            base_drawdown = volatility * 2  # 2x volatility as base
            risk_adjustment = risk_score * 0.1  # Additional risk-based drawdown

            # Profit magnitude adjustment
            profit_adjustment = abs(predicted_profit) * 0.5  # Higher profit targets = higher drawdown risk

            max_drawdown = base_drawdown + risk_adjustment + profit_adjustment

            return min(0.2, max(0.01, max_drawdown))  # Cap between 1% and 20%

        except Exception as e:
            logger.error(f"Error estimating max drawdown: {e}")
            return 0.05  # Default 5%

    def _get_market_cap_rank(self, symbol: str) -> float:
        """Get market cap rank for the symbol (simplified)"""
        # Simplified ranking based on common symbols
        major_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'XRP', 'DOT', 'AVAX']

        base_symbol = symbol.split('-')[0] if '-' in symbol else symbol.replace('USDT', '').replace('USD', '')

        if base_symbol in major_symbols:
            return 1.0 - (major_symbols.index(base_symbol) / len(major_symbols))
        else:
            return 0.3  # Default for unknown symbols

    def _get_volume_rank(self, symbol: str, market_data: Dict[str, Any]) -> float:
        """Get volume rank for the symbol"""
        try:
            price_data = market_data.get('price_data', {})
            if not price_data:
                return 0.5

            volumes = []
            symbol_volume = 0

            for sym, data in price_data.items():
                if isinstance(data, dict) and 'volume' in data:
                    volume = float(data['volume'])
                    volumes.append(volume)
                    if sym == symbol:
                        symbol_volume = volume

            if volumes and symbol_volume > 0:
                volumes.sort(reverse=True)
                rank = volumes.index(symbol_volume) / len(volumes) if symbol_volume in volumes else 0.5
                return 1.0 - rank  # Higher volume = higher rank

            return 0.5

        except Exception as e:
            logger.error(f"Error getting volume rank: {e}")
            return 0.5

    def _fallback_profit_prediction(self, symbol: str) -> ProfitPrediction:
        """ELIMINATED: NO FALLBACK PROFIT PREDICTIONS - SYSTEM MUST USE REAL NEURAL MODELS"""
        # CRITICAL: Fallback profit predictions have been eliminated to ensure only real neural models are used
        # The system must fail gracefully if neural models are unavailable rather than using simplified fallbacks

        logger.error(f"[CRITICAL] Neural profit prediction failed for {symbol} - NO FALLBACK ALLOWED")
        logger.error("[CRITICAL] System requires functional neural models - check model initialization")

        # Raise exception instead of providing fallback prediction
        raise RuntimeError(f"Neural profit prediction failed for {symbol} - no fallback available")

    def _enhance_features_with_timing(self, features: Dict[str, float], market_data: Dict[str, Any]) -> Dict[str, float]:
        """Enhance features with timing-based analysis"""
        try:
            enhanced_features = features.copy()

            # Add time-based features
            current_hour = datetime.now(timezone.utc).hour
            enhanced_features['hour_of_day'] = current_hour / 24.0  # Normalize to 0-1

            # Market session timing (assuming UTC)
            if 13 <= current_hour <= 21:  # US market hours
                enhanced_features['us_market_session'] = 1.0
            else:
                enhanced_features['us_market_session'] = 0.0

            if 8 <= current_hour <= 16:  # European market hours
                enhanced_features['eu_market_session'] = 1.0
            else:
                enhanced_features['eu_market_session'] = 0.0

            if 0 <= current_hour <= 8:  # Asian market hours
                enhanced_features['asian_market_session'] = 1.0
            else:
                enhanced_features['asian_market_session'] = 0.0

            # Weekend factor (crypto markets are 24/7 but may have different patterns)
            weekday = datetime.now(timezone.utc).weekday()
            enhanced_features['is_weekend'] = 1.0 if weekday >= 5 else 0.0

            # Time-based momentum calculation
            momentum_1h = enhanced_features.get('momentum_1h', 0.0)
            momentum_24h = enhanced_features.get('momentum_24h', 0.0)

            # Calculate time-weighted momentum
            if abs(momentum_1h) > abs(momentum_24h):
                enhanced_features['time_momentum_strength'] = 1.0  # Short-term momentum stronger
            else:
                enhanced_features['time_momentum_strength'] = 0.5  # Long-term momentum stronger

            return enhanced_features

        except Exception as e:
            logger.error(f"Error enhancing features with timing: {e}")
            return features

    def _apply_time_optimization(self, profit: float, features: Dict[str, float]) -> float:
        """Apply time-based optimization to profit prediction"""
        try:
            time_factor = self.profit_optimization_weights['time_factor']

            # Boost profit for optimal timing conditions
            us_session = features.get('us_market_session', 0.0)
            time_momentum = features.get('time_momentum_strength', 0.5)

            # US market session typically has higher volume and volatility
            session_boost = us_session * 0.1  # Up to 10% boost during US hours

            # Strong short-term momentum gets time boost
            momentum_boost = (time_momentum - 0.5) * 0.05  # Up to 2.5% boost

            # Apply time optimization
            time_optimized_profit = profit * (1 + (session_boost + momentum_boost) * time_factor)

            return time_optimized_profit

        except Exception as e:
            logger.error(f"Error applying time optimization: {e}")
            return profit

    def _calculate_optimal_prices_with_timing(self, current_price: float, predicted_profit: float,
                                            features: Dict[str, float]) -> Tuple[float, float]:
        """Calculate optimal entry and exit prices with timing considerations"""
        try:
            if current_price <= 0:
                return 0.0, 0.0

            # Base calculation
            optimal_entry, optimal_exit = self._calculate_optimal_prices(current_price, predicted_profit, features)

            # Time-based adjustments
            us_session = features.get('us_market_session', 0.0)
            volatility = features.get('volatility', 0.1)

            # During high-volume sessions, adjust entry timing
            if us_session > 0.5:
                # More aggressive entry during US hours due to higher liquidity
                entry_adjustment = volatility * 0.3
                if predicted_profit > 0:
                    optimal_entry = current_price * (1 - entry_adjustment)
                else:
                    optimal_entry = current_price * (1 + entry_adjustment)

            return optimal_entry, optimal_exit

        except Exception as e:
            logger.error(f"Error calculating optimal prices with timing: {e}")
            return current_price, current_price

    def _estimate_optimal_time_to_target(self, features: Dict[str, float], predicted_profit: float) -> int:
        """Estimate optimal time to target with enhanced timing analysis"""
        try:
            # Base estimation
            base_time = self._estimate_time_to_target(features, predicted_profit)

            # Time-based adjustments
            us_session = features.get('us_market_session', 0.0)
            time_momentum = features.get('time_momentum_strength', 0.5)

            # Faster execution during high-volume sessions
            if us_session > 0.5:
                base_time = int(base_time * 0.8)  # 20% faster during US hours

            # Adjust for momentum strength
            if time_momentum > 0.7:
                base_time = int(base_time * 0.7)  # 30% faster with strong momentum
            elif time_momentum < 0.3:
                base_time = int(base_time * 1.3)  # 30% slower with weak momentum

            return max(10, min(1440, base_time))  # Between 10 minutes and 24 hours

        except Exception as e:
            logger.error(f"Error estimating optimal time to target: {e}")
            return 120

    def _calculate_time_risk_factor(self, time_to_target: int) -> float:
        """Calculate additional risk factor based on time to target"""
        try:
            # Longer time horizons have higher uncertainty
            if time_to_target <= 30:  # 30 minutes or less
                return 0.0
            elif time_to_target <= 120:  # 2 hours or less
                return 0.05
            elif time_to_target <= 480:  # 8 hours or less
                return 0.1
            else:  # More than 8 hours
                return 0.15

        except Exception as e:
            logger.error(f"Error calculating time risk factor: {e}")
            return 0.1

    def _calculate_enhanced_profit_factors(self, features: Dict[str, float],
                                         sentiment_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """Calculate enhanced profit factors with timing considerations"""
        try:
            # Base factors
            factors = self._calculate_profit_factors(features, sentiment_data)

            # Add timing factors
            factors['timing_advantage'] = features.get('us_market_session', 0.0) * 0.5 + \
                                        features.get('time_momentum_strength', 0.5) * 0.5

            factors['session_liquidity'] = (features.get('us_market_session', 0.0) * 0.4 +
                                          features.get('eu_market_session', 0.0) * 0.3 +
                                          features.get('asian_market_session', 0.0) * 0.3)

            return factors

        except Exception as e:
            logger.error(f"Error calculating enhanced profit factors: {e}")
            return self._calculate_profit_factors(features, sentiment_data)

    def _generate_time_optimized_strategies(self, predicted_profit: float, risk_adjusted_profit: float,
                                          features: Dict[str, float], sentiment_data: Optional[Dict[str, Any]] = None,
                                          time_to_target: int = 120) -> List[str]:
        """Generate time-optimized profit strategies"""
        try:
            strategies = []

            # Base strategies
            base_strategies = self._generate_profit_strategies(predicted_profit, risk_adjusted_profit, features, sentiment_data)
            strategies.extend(base_strategies)

            # Time-specific strategies
            us_session = features.get('us_market_session', 0.0)
            time_momentum = features.get('time_momentum_strength', 0.5)

            if us_session > 0.5:
                strategies.append("Execute during US market hours for optimal liquidity")
                strategies.append("Consider larger position sizes due to higher volume")

            if time_to_target <= 60:  # 1 hour or less
                strategies.append("Fast execution strategy - monitor closely for quick exits")
                strategies.append("Use tight stop-losses due to short time horizon")
            elif time_to_target >= 480:  # 8 hours or more
                strategies.append("Long-term hold strategy - set wider stop-losses")
                strategies.append("Monitor for trend changes over extended period")

            if time_momentum > 0.7:
                strategies.append("Ride the momentum - strong short-term signals detected")
            elif time_momentum < 0.3:
                strategies.append("Contrarian opportunity - weak momentum may reverse")

            return strategies

        except Exception as e:
            logger.error(f"Error generating time-optimized strategies: {e}")
            return ["Hold position and monitor market conditions"]

    def _estimate_time_adjusted_drawdown(self, features: Dict[str, float], predicted_profit: float) -> float:
        """Estimate maximum drawdown with time considerations"""
        try:
            # Base drawdown calculation
            base_drawdown = self._estimate_max_drawdown(features, predicted_profit)

            # Time-based adjustments
            us_session = features.get('us_market_session', 0.0)
            volatility = features.get('volatility', 0.1)

            # Higher volatility during active sessions may increase drawdown
            if us_session > 0.5 and volatility > 0.2:
                time_adjusted_drawdown = base_drawdown * 1.2  # 20% higher drawdown risk
            else:
                time_adjusted_drawdown = base_drawdown

            return min(0.5, time_adjusted_drawdown)  # Cap at 50%

        except Exception as e:
            logger.error(f"Error estimating time-adjusted drawdown: {e}")
            return 0.1

    def _calculate_time_adjusted_probability(self, predicted_profit: float, features: Dict[str, float]) -> float:
        """Calculate probability with time-based adjustments"""
        try:
            # Base probability
            base_prob = self._calculate_profit_probability(predicted_profit, features)

            # Time-based adjustments
            timing_advantage = features.get('timing_advantage', 0.5)
            session_liquidity = features.get('session_liquidity', 0.5)

            # Better timing and liquidity increase probability
            time_boost = (timing_advantage + session_liquidity) * 0.1  # Up to 20% boost

            adjusted_prob = base_prob + time_boost

            return max(0.1, min(0.95, adjusted_prob))

        except Exception as e:
            logger.error(f"Error calculating time-adjusted probability: {e}")
            return 0.5

    def _store_prediction_for_accuracy_tracking(self, prediction: ProfitPrediction, features: Dict[str, float]):
        """Store prediction for accuracy tracking and learning"""
        try:
            self.prediction_accuracy['total_predictions'] += 1
            self.prediction_accuracy['profit_predicted'] += prediction.predicted_profit
            self.prediction_accuracy['last_updated'] = datetime.now(timezone.utc)

            # Store in profit history for later validation
            if prediction.symbol not in self.profit_history:
                self.profit_history[prediction.symbol] = []

            history_entry = {
                'timestamp': prediction.timestamp,
                'predicted_profit': prediction.predicted_profit,
                'confidence': prediction.confidence,
                'features': features.copy(),
                'time_to_target': prediction.time_to_target
            }

            self.profit_history[prediction.symbol].append(history_entry)

            # Keep only last 1000 predictions per symbol
            if len(self.profit_history[prediction.symbol]) > 1000:
                self.profit_history[prediction.symbol] = self.profit_history[prediction.symbol][-1000:]

            logger.debug(f"Stored profit prediction for accuracy tracking: {prediction.symbol} - Profit: {prediction.predicted_profit:.3f}")

        except Exception as e:
            logger.error(f"Error storing prediction for accuracy tracking: {e}")

    def get_prediction_accuracy_metrics(self) -> Dict[str, Any]:
        """Get prediction accuracy metrics"""
        try:
            return {
                'total_predictions': self.prediction_accuracy['total_predictions'],
                'accurate_predictions': self.prediction_accuracy['accurate_predictions'],
                'accuracy_rate': self.prediction_accuracy['accuracy_rate'],
                'profit_predicted': self.prediction_accuracy['profit_predicted'],
                'profit_realized': self.prediction_accuracy['profit_realized'],
                'last_updated': self.prediction_accuracy['last_updated'].isoformat(),
                'symbols_tracked': len(self.profit_history),
                'cache_size': len(self.profit_cache)
            }
        except Exception as e:
            logger.error(f"Error getting accuracy metrics: {e}")
            return {'error': str(e)}

    def update_profit_history(self, symbol: str, prediction: ProfitPrediction, actual_result: Optional[float] = None):
        """Update profit prediction history for learning"""
        try:
            if symbol not in self.profit_history:
                self.profit_history[symbol] = []

            history_entry = {
                'timestamp': prediction.timestamp,
                'predicted_profit': prediction.predicted_profit,
                'confidence': prediction.confidence,
                'actual_result': actual_result
            }

            self.profit_history[symbol].append(history_entry)

            # Keep only last 1000 predictions
            if len(self.profit_history[symbol]) > 1000:
                self.profit_history[symbol] = self.profit_history[symbol][-1000:]

        except Exception as e:
            logger.error(f"Error updating profit history: {e}")

    def _calculate_fear_greed_trend(self, current_fear_greed: float) -> float:
        """Calculate Fear & Greed Index trend pattern"""
        try:
            # Store historical Fear & Greed values for trend analysis
            if not hasattr(self, 'fear_greed_history'):
                self.fear_greed_history = []

            self.fear_greed_history.append(current_fear_greed)

            # Keep only last 24 values (24 hours if updated hourly)
            if len(self.fear_greed_history) > 24:
                self.fear_greed_history = self.fear_greed_history[-24:]

            # Calculate trend if we have enough data
            if len(self.fear_greed_history) >= 3:
                recent_avg = sum(self.fear_greed_history[-3:]) / 3
                older_avg = sum(self.fear_greed_history[-6:-3]) / 3 if len(self.fear_greed_history) >= 6 else recent_avg

                # Normalize trend to -1 to 1 scale
                trend = (recent_avg - older_avg) / 100.0
                return max(-1.0, min(1.0, trend))

            return 0.0  # No trend if insufficient data

        except Exception as e:
            logger.error(f"Error calculating Fear & Greed trend: {e}")
            return 0.0

    def _calculate_fear_greed_momentum(self, current_fear_greed: float) -> float:
        """Calculate Fear & Greed Index momentum (rate of change)"""
        try:
            if not hasattr(self, 'fear_greed_history') or len(self.fear_greed_history) < 2:
                return 0.0

            # Calculate momentum as rate of change
            previous_value = self.fear_greed_history[-2] if len(self.fear_greed_history) >= 2 else current_fear_greed
            momentum = (current_fear_greed - previous_value) / 100.0

            # Normalize momentum to -1 to 1 scale
            return max(-1.0, min(1.0, momentum * 5))  # Amplify for sensitivity

        except Exception as e:
            logger.error(f"Error calculating Fear & Greed momentum: {e}")
            return 0.0

    def _determine_market_regime_from_fear_greed(self, fear_greed_index: float) -> float:
        """Determine market regime based on Fear & Greed Index"""
        try:
            # Market regime classification based on Fear & Greed Index
            if fear_greed_index <= 25:
                return 0.0  # Extreme Fear - potential buying opportunity
            elif fear_greed_index <= 45:
                return 0.25  # Fear - cautious optimism
            elif fear_greed_index <= 55:
                return 0.5  # Neutral - balanced market
            elif fear_greed_index <= 75:
                return 0.75  # Greed - potential profit taking
            else:
                return 1.0  # Extreme Greed - high risk of correction

        except Exception as e:
            logger.error(f"Error determining market regime from Fear & Greed: {e}")
            return 0.5  # Default to neutral

    async def ensemble_predict_profit(self, symbol: str, market_data: Dict[str, Any],
                                    amount: float) -> Dict[str, Any]:
        """Advanced ensemble profit prediction using multiple models"""
        try:
            start_time = time.time()

            # Get predictions from multiple models
            predictions = []
            weights = []

            # 1. Enhanced LSTM prediction
            lstm_pred = await self.predict_profit(symbol, market_data, amount)
            predictions.append(lstm_pred['expected_profit'])
            weights.append(0.4)  # 40% weight for LSTM

            # 2. Transformer-based prediction
            transformer_pred = await self._transformer_predict_profit(symbol, market_data, amount)
            predictions.append(transformer_pred)
            weights.append(0.3)  # 30% weight for Transformer

            # 3. Graph Neural Network prediction
            gnn_pred = await self._gnn_predict_profit(symbol, market_data, amount)
            predictions.append(gnn_pred)
            weights.append(0.2)  # 20% weight for GNN

            # 4. Classical ML prediction (XGBoost/Random Forest)
            classical_pred = await self._classical_ml_predict_profit(symbol, market_data, amount)
            predictions.append(classical_pred)
            weights.append(0.1)  # 10% weight for Classical ML

            # Ensemble prediction (weighted average)
            ensemble_profit = sum(p * w for p, w in zip(predictions, weights))

            # Calculate prediction uncertainty
            prediction_std = np.std(predictions)
            confidence = max(0.1, 1.0 - (prediction_std / abs(ensemble_profit + 1e-8)))

            # Advanced risk assessment
            risk_metrics = await self._calculate_advanced_risk_metrics(
                symbol, market_data, predictions, prediction_std
            )

            execution_time = (time.time() - start_time) * 1000

            return {
                'expected_profit': ensemble_profit,
                'confidence': confidence,
                'prediction_uncertainty': prediction_std,
                'individual_predictions': {
                    'lstm': predictions[0],
                    'transformer': predictions[1],
                    'gnn': predictions[2],
                    'classical_ml': predictions[3]
                },
                'risk_metrics': risk_metrics,
                'execution_time_ms': execution_time,
                'model_type': 'ensemble',
                'ensemble_weights': dict(zip(['lstm', 'transformer', 'gnn', 'classical_ml'], weights))
            }

        except Exception as e:
            logger.error(f"❌ [ENSEMBLE] Error in ensemble profit prediction: {e}")
            # Fallback to single model prediction
            return await self.predict_profit(symbol, market_data, amount)

    async def _transformer_predict_profit(self, symbol: str, market_data: Dict[str, Any],
                                        amount: float) -> float:
        """Transformer-based profit prediction"""
        try:
            # Simple transformer-like calculation for now
            features = await self._extract_profit_features(symbol, market_data)

            # Attention-like weighted feature combination
            feature_values = list(features.values())[:10]  # Use first 10 features
            attention_weights = [0.15, 0.12, 0.1, 0.1, 0.08, 0.08, 0.07, 0.07, 0.06, 0.06]

            if len(feature_values) >= len(attention_weights):
                expected_return = sum(f * w for f, w in zip(feature_values, attention_weights))
            else:
                expected_return = np.mean(feature_values) * 0.08

            profit = amount * expected_return
            return profit

        except Exception as e:
            logger.debug(f"🔍 [TRANSFORMER] Transformer prediction error: {e}")
            return amount * 0.005

    async def _gnn_predict_profit(self, symbol: str, market_data: Dict[str, Any],
                                amount: float) -> float:
        """Graph Neural Network profit prediction"""
        try:
            # Simulate graph-based prediction using correlation features
            features = await self._extract_profit_features(symbol, market_data)

            # Graph-like feature aggregation
            correlation_features = [
                features.get('momentum_1h', 0.0),
                features.get('momentum_4h', 0.0),
                features.get('momentum_24h', 0.0),
                features.get('volatility', 0.0),
                features.get('volume_ratio', 0.0)
            ]

            # Simulate graph convolution
            aggregated_signal = np.mean(correlation_features) * 1.2  # Graph amplification
            expected_return = np.tanh(aggregated_signal) * 0.05  # Bounded return

            profit = amount * expected_return
            return profit

        except Exception as e:
            logger.debug(f"🔍 [GNN] GNN prediction error: {e}")
            return amount * 0.003

    async def _classical_ml_predict_profit(self, symbol: str, market_data: Dict[str, Any],
                                         amount: float) -> float:
        """Classical ML profit prediction using ensemble of traditional models"""
        try:
            # Extract features for classical ML
            features = await self._extract_profit_features(symbol, market_data)

            # Simple linear model prediction (can be replaced with XGBoost/RandomForest)
            feature_values = list(features.values())

            # Weighted feature combination (simplified)
            weights = [0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.05, 0.05]  # Example weights
            if len(feature_values) >= len(weights):
                expected_return = sum(f * w for f, w in zip(feature_values[:len(weights)], weights))
            else:
                expected_return = np.mean(feature_values) * 0.1

            profit = amount * expected_return
            return profit

        except Exception as e:
            logger.debug(f"🔍 [CLASSICAL] Classical ML prediction error: {e}")
            return amount * 0.002

    async def _calculate_advanced_risk_metrics(self, symbol: str, market_data: Dict[str, Any],
                                             predictions: List[float], uncertainty: float) -> Dict[str, float]:
        """Calculate advanced risk metrics for profit prediction"""
        try:
            # Value at Risk (VaR) calculation
            var_95 = np.percentile(predictions, 5)  # 95% VaR
            var_99 = np.percentile(predictions, 1)  # 99% VaR

            # Expected Shortfall (Conditional VaR)
            expected_shortfall = np.mean([p for p in predictions if p <= var_95])

            # Maximum Drawdown estimation
            max_drawdown = min(predictions) / max(predictions, default=1.0)

            # Sharpe Ratio estimation
            mean_return = np.mean(predictions)
            std_return = np.std(predictions)
            sharpe_ratio = mean_return / (std_return + 1e-8)

            # Volatility-adjusted return
            volatility = market_data.get('volatility', 0.2)
            vol_adjusted_return = mean_return / (volatility + 1e-8)

            return {
                'var_95': var_95,
                'var_99': var_99,
                'expected_shortfall': expected_shortfall,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'volatility_adjusted_return': vol_adjusted_return,
                'prediction_uncertainty': uncertainty,
                'risk_score': min(1.0, uncertainty + abs(max_drawdown))
            }

        except Exception as e:
            logger.error(f"❌ [RISK] Error calculating risk metrics: {e}")
            return {
                'var_95': 0.0,
                'var_99': 0.0,
                'expected_shortfall': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'volatility_adjusted_return': 0.0,
                'prediction_uncertainty': uncertainty,
                'risk_score': 0.5
            }

    def learn_from_trade(self, symbol: str, strategy: str, entry_price: float, exit_price: float,
                        quantity: float, profit_loss: float, duration_minutes: float,
                        market_conditions: Dict[str, float] = None) -> bool:
        """Learn from completed trade to improve future predictions"""
        try:
            logger.debug(f"[PROFIT-PREDICTOR] Learning from {symbol} trade: P&L=${profit_loss:.4f}")

            # Calculate trade metrics
            return_pct = (exit_price - entry_price) / entry_price * 100 if entry_price > 0 else 0.0
            profit_per_minute = profit_loss / max(duration_minutes, 1.0)

            # Create trade record
            trade_record = {
                'symbol': symbol,
                'strategy': strategy,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'quantity': quantity,
                'profit_loss': profit_loss,
                'return_pct': return_pct,
                'duration_minutes': duration_minutes,
                'profit_per_minute': profit_per_minute,
                'timestamp': datetime.now(timezone.utc),
                'market_conditions': market_conditions or {}
            }

            # Update strategy performance tracking
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = {
                    'total_trades': 0,
                    'total_profit': 0.0,
                    'win_rate': 0.0,
                    'avg_return': 0.0,
                    'avg_duration': 0.0,
                    'last_updated': datetime.now(timezone.utc)
                }

            perf = self.strategy_performance[strategy]
            perf['total_trades'] += 1
            perf['total_profit'] += profit_loss

            # Update win rate
            if profit_loss > 0:
                wins = perf.get('wins', 0) + 1
                perf['wins'] = wins
                perf['win_rate'] = wins / perf['total_trades'] * 100

            # Update averages
            perf['avg_return'] = (perf.get('avg_return', 0.0) * (perf['total_trades'] - 1) + return_pct) / perf['total_trades']
            perf['avg_duration'] = (perf.get('avg_duration', 0.0) * (perf['total_trades'] - 1) + duration_minutes) / perf['total_trades']
            perf['last_updated'] = datetime.now(timezone.utc)

            # Update symbol-specific learning
            if symbol not in self.symbol_performance:
                self.symbol_performance[symbol] = {
                    'total_trades': 0,
                    'total_profit': 0.0,
                    'volatility_estimate': 0.02,  # Default 2%
                    'price_momentum': 0.0,
                    'last_price': exit_price
                }

            sym_perf = self.symbol_performance[symbol]
            sym_perf['total_trades'] += 1
            sym_perf['total_profit'] += profit_loss

            # Update volatility estimate
            if sym_perf.get('last_price', 0) > 0:
                price_change = abs(exit_price - sym_perf['last_price']) / sym_perf['last_price']
                sym_perf['volatility_estimate'] = (sym_perf['volatility_estimate'] * 0.9 + price_change * 0.1)

            # Update momentum
            if sym_perf.get('last_price', 0) > 0:
                momentum = (exit_price - sym_perf['last_price']) / sym_perf['last_price']
                sym_perf['price_momentum'] = (sym_perf.get('price_momentum', 0.0) * 0.8 + momentum * 0.2)

            sym_perf['last_price'] = exit_price

            logger.debug(f"[PROFIT-PREDICTOR] Updated learning for {symbol} {strategy}: "
                        f"Total trades: {perf['total_trades']}, Win rate: {perf['win_rate']:.1f}%")

            return True

        except Exception as e:
            logger.error(f"[PROFIT-PREDICTOR] Error learning from trade: {e}")
            return False

# Export the main classes
__all__ = ['EnhancedProfitPredictor', 'ProfitPrediction']
