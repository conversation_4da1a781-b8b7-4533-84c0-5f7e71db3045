"""
Live Data Monitor for AutoGPT Trader
Real-time monitoring of data feeds and market data quality
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import time
from collections import deque

logger = logging.getLogger(__name__)

class LiveDataMonitor:
    """
    Real-time monitoring system for data feeds and market data quality
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.data_sources = {}
        self.quality_metrics = {}
        self.alert_thresholds = {
            'max_latency_ms': 1000,
            'min_update_frequency': 1.0,  # Updates per second
            'max_error_rate': 0.05        # 5% error rate
        }
        self.monitoring_active = False
        self.data_history = deque(maxlen=1000)
        
        logger.info("📊 [LIVE-DATA-MONITOR] Live data monitoring system initialized")
    
    async def start_monitoring(self) -> None:
        """Start live data monitoring"""
        try:
            self.monitoring_active = True
            logger.info("🔍 [LIVE-DATA-MONITOR] Starting live data monitoring...")
            
            # Start monitoring tasks
            asyncio.create_task(self._monitor_data_quality())
            asyncio.create_task(self._monitor_latency())
            asyncio.create_task(self._monitor_error_rates())
            
            logger.info("✅ [LIVE-DATA-MONITOR] Live data monitoring started")
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error starting monitoring: {e}")
    
    async def stop_monitoring(self) -> None:
        """Stop live data monitoring"""
        try:
            self.monitoring_active = False
            logger.info("🛑 [LIVE-DATA-MONITOR] Live data monitoring stopped")
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error stopping monitoring: {e}")
    
    async def register_data_source(self, source_name: str, source_config: Dict[str, Any]) -> None:
        """Register a data source for monitoring"""
        try:
            self.data_sources[source_name] = {
                'config': source_config,
                'last_update': None,
                'update_count': 0,
                'error_count': 0,
                'latency_history': deque(maxlen=100),
                'status': 'active'
            }
            
            self.quality_metrics[source_name] = {
                'avg_latency_ms': 0.0,
                'update_frequency': 0.0,
                'error_rate': 0.0,
                'uptime_percentage': 100.0
            }
            
            logger.info(f"📊 [LIVE-DATA-MONITOR] Registered data source: {source_name}")
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error registering data source {source_name}: {e}")
    
    async def record_data_update(self, source_name: str, data: Dict[str, Any], latency_ms: float = None) -> None:
        """Record a data update from a source"""
        try:
            if source_name not in self.data_sources:
                await self.register_data_source(source_name, {})
            
            source = self.data_sources[source_name]
            current_time = time.time()
            
            # Update source metrics
            source['last_update'] = current_time
            source['update_count'] += 1
            
            if latency_ms is not None:
                source['latency_history'].append(latency_ms)
            
            # Record in history
            self.data_history.append({
                'timestamp': current_time,
                'source': source_name,
                'data_size': len(str(data)),
                'latency_ms': latency_ms
            })
            
            # Update quality metrics
            await self._update_quality_metrics(source_name)
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error recording data update for {source_name}: {e}")
    
    async def record_data_error(self, source_name: str, error: str) -> None:
        """Record a data error from a source"""
        try:
            if source_name not in self.data_sources:
                await self.register_data_source(source_name, {})
            
            source = self.data_sources[source_name]
            source['error_count'] += 1
            
            # Update quality metrics
            await self._update_quality_metrics(source_name)
            
            # Check if error rate exceeds threshold
            error_rate = self.quality_metrics[source_name]['error_rate']
            if error_rate > self.alert_thresholds['max_error_rate']:
                await self._trigger_alert(source_name, 'high_error_rate', error_rate)
            
            logger.warning(f"⚠️ [LIVE-DATA-MONITOR] Data error in {source_name}: {error}")
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error recording data error for {source_name}: {e}")
    
    async def get_data_quality_report(self) -> Dict[str, Any]:
        """Get comprehensive data quality report"""
        try:
            report = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'monitoring_active': self.monitoring_active,
                'total_sources': len(self.data_sources),
                'sources': {},
                'overall_health': 'healthy'
            }
            
            unhealthy_sources = 0
            
            for source_name, metrics in self.quality_metrics.items():
                source_health = 'healthy'
                
                # Check health indicators
                if metrics['error_rate'] > self.alert_thresholds['max_error_rate']:
                    source_health = 'unhealthy'
                    unhealthy_sources += 1
                elif metrics['avg_latency_ms'] > self.alert_thresholds['max_latency_ms']:
                    source_health = 'degraded'
                elif metrics['update_frequency'] < self.alert_thresholds['min_update_frequency']:
                    source_health = 'slow'
                
                report['sources'][source_name] = {
                    'health': source_health,
                    'metrics': metrics,
                    'last_update': self.data_sources[source_name]['last_update']
                }
            
            # Overall health assessment
            if unhealthy_sources > 0:
                report['overall_health'] = 'unhealthy'
            elif unhealthy_sources > len(self.data_sources) * 0.3:
                report['overall_health'] = 'degraded'
            
            return report
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error generating quality report: {e}")
            return {'error': str(e)}
    
    async def _update_quality_metrics(self, source_name: str) -> None:
        """Update quality metrics for a data source"""
        try:
            source = self.data_sources[source_name]
            metrics = self.quality_metrics[source_name]
            
            # Calculate average latency
            if source['latency_history']:
                metrics['avg_latency_ms'] = sum(source['latency_history']) / len(source['latency_history'])
            
            # Calculate error rate
            total_operations = source['update_count'] + source['error_count']
            if total_operations > 0:
                metrics['error_rate'] = source['error_count'] / total_operations
            
            # Calculate update frequency (updates per second)
            if source['last_update'] and source['update_count'] > 1:
                time_span = time.time() - (source['last_update'] - 60)  # Last minute
                if time_span > 0:
                    recent_updates = min(source['update_count'], 60)  # Approximate recent updates
                    metrics['update_frequency'] = recent_updates / time_span
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error updating quality metrics for {source_name}: {e}")
    
    async def _monitor_data_quality(self) -> None:
        """Background task to monitor data quality"""
        try:
            while self.monitoring_active:
                for source_name in self.data_sources.keys():
                    await self._update_quality_metrics(source_name)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error in data quality monitoring: {e}")
    
    async def _monitor_latency(self) -> None:
        """Background task to monitor latency"""
        try:
            while self.monitoring_active:
                for source_name, metrics in self.quality_metrics.items():
                    if metrics['avg_latency_ms'] > self.alert_thresholds['max_latency_ms']:
                        await self._trigger_alert(source_name, 'high_latency', metrics['avg_latency_ms'])
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error in latency monitoring: {e}")
    
    async def _monitor_error_rates(self) -> None:
        """Background task to monitor error rates"""
        try:
            while self.monitoring_active:
                for source_name, metrics in self.quality_metrics.items():
                    if metrics['error_rate'] > self.alert_thresholds['max_error_rate']:
                        await self._trigger_alert(source_name, 'high_error_rate', metrics['error_rate'])
                
                await asyncio.sleep(60)  # Check every minute
                
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error in error rate monitoring: {e}")
    
    async def _trigger_alert(self, source_name: str, alert_type: str, value: float) -> None:
        """Trigger an alert for data quality issues"""
        try:
            alert_message = f"Data quality alert: {source_name} - {alert_type}: {value}"
            logger.warning(f"🚨 [LIVE-DATA-MONITOR] {alert_message}")
            
            # Here you could integrate with external alerting systems
            # For now, just log the alert
            
        except Exception as e:
            logger.error(f"❌ [LIVE-DATA-MONITOR] Error triggering alert: {e}")


# Global instance for easy access
live_data_monitor = LiveDataMonitor()
