"""
Capital Management System for AutoGPT Trader
Handles portfolio allocation, risk management, and capital optimization
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone
import json

logger = logging.getLogger(__name__)

class CapitalManager:
    """
    Advanced capital management system for real money trading
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.total_capital = Decimal('0.0')
        self.allocated_capital = {}
        self.risk_limits = {
            'max_position_size': 0.1,  # 10% max per position
            'max_daily_loss': 0.05,    # 5% max daily loss
            'max_drawdown': 0.15       # 15% max drawdown
        }
        self.performance_metrics = {
            'total_profit': Decimal('0.0'),
            'total_trades': 0,
            'win_rate': 0.0,
            'sharpe_ratio': 0.0
        }
        
        logger.info("💰 [CAPITAL] Capital management system initialized")
    
    async def get_available_capital(self, exchange: str = None) -> Decimal:
        """Get available capital for trading"""
        try:
            if exchange:
                return self.allocated_capital.get(exchange, Decimal('0.0'))
            return self.total_capital
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error getting available capital: {e}")
            return Decimal('0.0')
    
    async def allocate_capital(self, exchange: str, amount: Decimal) -> bool:
        """Allocate capital to specific exchange"""
        try:
            if amount <= self.total_capital:
                self.allocated_capital[exchange] = amount
                logger.info(f"💰 [CAPITAL] Allocated {amount} to {exchange}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error allocating capital: {e}")
            return False
    
    async def calculate_position_size(self, symbol: str, confidence: float, available_balance: float) -> float:
        """Calculate optimal position size based on confidence and risk management"""
        try:
            # Base position size (80-90% of available balance for aggressive trading)
            base_size = available_balance * 0.85
            
            # Adjust based on confidence (minimum 60% confidence required)
            if confidence < 0.60:
                return 0.0
            
            # Scale position size by confidence
            confidence_multiplier = min(confidence * 1.2, 1.0)  # Cap at 100%
            position_size = base_size * confidence_multiplier
            
            # Apply risk limits
            max_position = available_balance * self.risk_limits['max_position_size']
            position_size = min(position_size, max_position)
            
            # Ensure minimum position size for micro-trading
            if position_size < 0.90:  # $0.90 USDT minimum
                return 0.0
            
            return position_size
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error calculating position size: {e}")
            return 0.0
    
    async def update_performance_metrics(self, trade_result: Dict[str, Any]) -> None:
        """Update performance metrics from trade results"""
        try:
            profit = trade_result.get('profit', 0.0)
            self.performance_metrics['total_profit'] += Decimal(str(profit))
            self.performance_metrics['total_trades'] += 1
            
            # Update win rate
            if profit > 0:
                wins = getattr(self, '_wins', 0) + 1
                self._wins = wins
                self.performance_metrics['win_rate'] = wins / self.performance_metrics['total_trades']
            
            logger.debug(f"📊 [CAPITAL] Performance updated: profit {profit}, total trades {self.performance_metrics['total_trades']}")
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error updating performance metrics: {e}")
    
    async def check_risk_limits(self, proposed_trade: Dict[str, Any]) -> bool:
        """Check if proposed trade violates risk limits"""
        try:
            position_size = proposed_trade.get('position_size', 0.0)
            available_balance = proposed_trade.get('available_balance', 0.0)
            
            # Check position size limit
            if available_balance > 0:
                position_ratio = position_size / available_balance
                if position_ratio > self.risk_limits['max_position_size']:
                    logger.warning(f"⚠️ [CAPITAL] Position size exceeds limit: {position_ratio:.2%}")
                    return False
            
            # Check daily loss limit (simplified)
            daily_loss_ratio = abs(float(self.performance_metrics['total_profit'])) / max(float(self.total_capital), 1.0)
            if daily_loss_ratio > self.risk_limits['max_daily_loss']:
                logger.warning(f"⚠️ [CAPITAL] Daily loss limit exceeded: {daily_loss_ratio:.2%}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error checking risk limits: {e}")
            return False
    
    async def optimize_capital_allocation(self, exchange_performance: Dict[str, float]) -> Dict[str, Decimal]:
        """Optimize capital allocation across exchanges based on performance"""
        try:
            total_performance = sum(exchange_performance.values())
            optimized_allocation = {}
            
            if total_performance > 0:
                for exchange, performance in exchange_performance.items():
                    allocation_ratio = performance / total_performance
                    optimized_allocation[exchange] = self.total_capital * Decimal(str(allocation_ratio))
            else:
                # Equal allocation if no performance data
                equal_share = self.total_capital / len(exchange_performance)
                for exchange in exchange_performance.keys():
                    optimized_allocation[exchange] = equal_share
            
            return optimized_allocation
            
        except Exception as e:
            logger.error(f"❌ [CAPITAL] Error optimizing capital allocation: {e}")
            return {}


class WalletManager:
    """
    Wallet management for multi-exchange operations
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.wallets = {}
        self.transfer_history = []
        
        logger.info("👛 [WALLET] Wallet management system initialized")
    
    async def get_wallet_balance(self, exchange: str, currency: str) -> Decimal:
        """Get wallet balance for specific exchange and currency"""
        try:
            if exchange in self.wallets and currency in self.wallets[exchange]:
                return self.wallets[exchange][currency]
            return Decimal('0.0')
            
        except Exception as e:
            logger.error(f"❌ [WALLET] Error getting wallet balance: {e}")
            return Decimal('0.0')
    
    async def update_wallet_balance(self, exchange: str, currency: str, balance: Decimal) -> None:
        """Update wallet balance"""
        try:
            if exchange not in self.wallets:
                self.wallets[exchange] = {}
            
            self.wallets[exchange][currency] = balance
            logger.debug(f"👛 [WALLET] Updated {exchange} {currency} balance: {balance}")
            
        except Exception as e:
            logger.error(f"❌ [WALLET] Error updating wallet balance: {e}")
    
    async def transfer_funds(self, from_exchange: str, to_exchange: str, currency: str, amount: Decimal) -> bool:
        """Transfer funds between exchanges (simulated)"""
        try:
            from_balance = await self.get_wallet_balance(from_exchange, currency)
            
            if from_balance >= amount:
                # Deduct from source
                await self.update_wallet_balance(from_exchange, currency, from_balance - amount)
                
                # Add to destination
                to_balance = await self.get_wallet_balance(to_exchange, currency)
                await self.update_wallet_balance(to_exchange, currency, to_balance + amount)
                
                # Record transfer
                self.transfer_history.append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'from_exchange': from_exchange,
                    'to_exchange': to_exchange,
                    'currency': currency,
                    'amount': float(amount)
                })
                
                logger.info(f"💸 [WALLET] Transferred {amount} {currency} from {from_exchange} to {to_exchange}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [WALLET] Error transferring funds: {e}")
            return False
