"""
Profit Tracker for AutoGPT Trader
Tracks and analyzes trading profits and performance
"""

import logging
from typing import Dict, List, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone, timedelta
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class ProfitTracker:
    """
    Advanced profit tracking and analysis system
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.data_dir = Path("data/profit_tracking")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.trades = []
        self.daily_profits = {}
        self.strategy_performance = {}
        self.total_profit = Decimal('0.0')
        self.total_trades = 0
        self.win_rate = 0.0
        
        logger.info("💰 [PROFIT-TRACKER] Profit tracking system initialized")
    
    async def record_trade(self, trade_data: Dict[str, Any]) -> None:
        """Record a completed trade"""
        try:
            trade_record = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'symbol': trade_data.get('symbol', 'UNKNOWN'),
                'strategy': trade_data.get('strategy', 'unknown'),
                'profit': Decimal(str(trade_data.get('profit', 0.0))),
                'position_size': Decimal(str(trade_data.get('position_size', 0.0))),
                'confidence': trade_data.get('confidence', 0.0),
                'execution_time': trade_data.get('execution_time', 0.0),
                'fees': Decimal(str(trade_data.get('fees', 0.0)))
            }
            
            # Add to trades list
            self.trades.append(trade_record)
            
            # Update totals
            profit = trade_record['profit']
            self.total_profit += profit
            self.total_trades += 1
            
            # Update daily profits
            today = datetime.now(timezone.utc).date().isoformat()
            if today not in self.daily_profits:
                self.daily_profits[today] = Decimal('0.0')
            self.daily_profits[today] += profit
            
            # Update strategy performance
            strategy = trade_record['strategy']
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = {
                    'total_profit': Decimal('0.0'),
                    'total_trades': 0,
                    'wins': 0,
                    'losses': 0
                }
            
            self.strategy_performance[strategy]['total_profit'] += profit
            self.strategy_performance[strategy]['total_trades'] += 1
            
            if profit > 0:
                self.strategy_performance[strategy]['wins'] += 1
            else:
                self.strategy_performance[strategy]['losses'] += 1
            
            # Update overall win rate
            total_wins = sum(1 for trade in self.trades if trade['profit'] > 0)
            self.win_rate = total_wins / self.total_trades if self.total_trades > 0 else 0.0
            
            # Save to file
            await self._save_trade_data()
            
            logger.info(f"💰 [PROFIT-TRACKER] Trade recorded: {trade_record['symbol']} "
                       f"profit: {profit:.4f}, total: {self.total_profit:.4f}")
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error recording trade: {e}")
    
    async def get_profit_summary(self) -> Dict[str, Any]:
        """Get comprehensive profit summary"""
        try:
            # Calculate time-based metrics
            now = datetime.now(timezone.utc)
            today = now.date().isoformat()
            yesterday = (now - timedelta(days=1)).date().isoformat()
            this_week_start = (now - timedelta(days=7)).date().isoformat()
            
            # Daily profits
            today_profit = float(self.daily_profits.get(today, Decimal('0.0')))
            yesterday_profit = float(self.daily_profits.get(yesterday, Decimal('0.0')))
            
            # Weekly profit
            week_profit = sum(
                float(profit) for date, profit in self.daily_profits.items()
                if date >= this_week_start
            )
            
            # Best and worst trades
            if self.trades:
                best_trade = max(self.trades, key=lambda x: x['profit'])
                worst_trade = min(self.trades, key=lambda x: x['profit'])
            else:
                best_trade = worst_trade = None
            
            # Average profit per trade
            avg_profit = float(self.total_profit) / self.total_trades if self.total_trades > 0 else 0.0
            
            return {
                'timestamp': now.isoformat(),
                'total_profit': float(self.total_profit),
                'total_trades': self.total_trades,
                'win_rate': self.win_rate,
                'average_profit_per_trade': avg_profit,
                'today_profit': today_profit,
                'yesterday_profit': yesterday_profit,
                'week_profit': week_profit,
                'best_trade': {
                    'symbol': best_trade['symbol'] if best_trade else None,
                    'profit': float(best_trade['profit']) if best_trade else 0.0,
                    'timestamp': best_trade['timestamp'] if best_trade else None
                } if best_trade else None,
                'worst_trade': {
                    'symbol': worst_trade['symbol'] if worst_trade else None,
                    'profit': float(worst_trade['profit']) if worst_trade else 0.0,
                    'timestamp': worst_trade['timestamp'] if worst_trade else None
                } if worst_trade else None,
                'strategy_performance': {
                    strategy: {
                        'total_profit': float(data['total_profit']),
                        'total_trades': data['total_trades'],
                        'win_rate': data['wins'] / data['total_trades'] if data['total_trades'] > 0 else 0.0,
                        'avg_profit': float(data['total_profit']) / data['total_trades'] if data['total_trades'] > 0 else 0.0
                    }
                    for strategy, data in self.strategy_performance.items()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error generating profit summary: {e}")
            return {'error': str(e)}
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics"""
        try:
            if not self.trades:
                return {'error': 'No trades recorded'}
            
            profits = [float(trade['profit']) for trade in self.trades]
            
            # Calculate Sharpe ratio (simplified)
            avg_return = sum(profits) / len(profits)
            return_std = (sum((p - avg_return) ** 2 for p in profits) / len(profits)) ** 0.5
            sharpe_ratio = avg_return / return_std if return_std > 0 else 0.0
            
            # Calculate maximum drawdown
            cumulative_profits = []
            running_total = 0.0
            for profit in profits:
                running_total += profit
                cumulative_profits.append(running_total)
            
            peak = cumulative_profits[0]
            max_drawdown = 0.0
            for value in cumulative_profits:
                if value > peak:
                    peak = value
                drawdown = peak - value
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            # Profit factor
            gross_profit = sum(p for p in profits if p > 0)
            gross_loss = abs(sum(p for p in profits if p < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            return {
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'profit_factor': profit_factor,
                'gross_profit': gross_profit,
                'gross_loss': gross_loss,
                'largest_win': max(profits),
                'largest_loss': min(profits),
                'average_win': sum(p for p in profits if p > 0) / len([p for p in profits if p > 0]) if any(p > 0 for p in profits) else 0.0,
                'average_loss': sum(p for p in profits if p < 0) / len([p for p in profits if p < 0]) if any(p < 0 for p in profits) else 0.0
            }
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error calculating performance metrics: {e}")
            return {'error': str(e)}
    
    async def _save_trade_data(self) -> None:
        """Save trade data to file"""
        try:
            trade_file = self.data_dir / "trades.json"
            
            # Convert Decimal to float for JSON serialization
            trades_data = []
            for trade in self.trades:
                trade_copy = trade.copy()
                trade_copy['profit'] = float(trade_copy['profit'])
                trade_copy['position_size'] = float(trade_copy['position_size'])
                trade_copy['fees'] = float(trade_copy['fees'])
                trades_data.append(trade_copy)
            
            with open(trade_file, 'w') as f:
                json.dump(trades_data, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error saving trade data: {e}")
    
    async def load_trade_data(self) -> None:
        """Load trade data from file"""
        try:
            trade_file = self.data_dir / "trades.json"
            
            if trade_file.exists():
                with open(trade_file, 'r') as f:
                    trades_data = json.load(f)
                
                # Convert back to Decimal
                for trade_data in trades_data:
                    trade_data['profit'] = Decimal(str(trade_data['profit']))
                    trade_data['position_size'] = Decimal(str(trade_data['position_size']))
                    trade_data['fees'] = Decimal(str(trade_data['fees']))
                
                self.trades = trades_data
                
                # Recalculate metrics
                await self._recalculate_metrics()
                
                logger.info(f"💰 [PROFIT-TRACKER] Loaded {len(self.trades)} trades from file")
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error loading trade data: {e}")
    
    async def _recalculate_metrics(self) -> None:
        """Recalculate all metrics from loaded data"""
        try:
            self.total_profit = Decimal('0.0')
            self.total_trades = len(self.trades)
            self.daily_profits = {}
            self.strategy_performance = {}
            
            for trade in self.trades:
                profit = trade['profit']
                self.total_profit += profit
                
                # Daily profits
                trade_date = datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')).date().isoformat()
                if trade_date not in self.daily_profits:
                    self.daily_profits[trade_date] = Decimal('0.0')
                self.daily_profits[trade_date] += profit
                
                # Strategy performance
                strategy = trade['strategy']
                if strategy not in self.strategy_performance:
                    self.strategy_performance[strategy] = {
                        'total_profit': Decimal('0.0'),
                        'total_trades': 0,
                        'wins': 0,
                        'losses': 0
                    }
                
                self.strategy_performance[strategy]['total_profit'] += profit
                self.strategy_performance[strategy]['total_trades'] += 1
                
                if profit > 0:
                    self.strategy_performance[strategy]['wins'] += 1
                else:
                    self.strategy_performance[strategy]['losses'] += 1
            
            # Win rate
            total_wins = sum(1 for trade in self.trades if trade['profit'] > 0)
            self.win_rate = total_wins / self.total_trades if self.total_trades > 0 else 0.0
            
        except Exception as e:
            logger.error(f"❌ [PROFIT-TRACKER] Error recalculating metrics: {e}")
