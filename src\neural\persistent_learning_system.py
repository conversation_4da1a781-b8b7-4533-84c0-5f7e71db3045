"""
Persistent Learning System for AutoGPT-Trader
Ensures the system maintains and builds upon its learning across restarts
"""

import asyncio
import logging
import json
import pickle
import time
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class LearningSession:
    """Represents a learning session"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    trades_executed: int
    profit_generated: float
    strategies_learned: List[str]
    market_conditions: Dict[str, Any]
    performance_metrics: Dict[str, float]
    neural_weights_checksum: str
    version: str

@dataclass
class SystemMemory:
    """Persistent system memory"""
    last_shutdown_time: datetime
    total_runtime_hours: float
    total_trades: int
    total_profit: float
    learned_patterns: Dict[str, Any]
    strategy_performance: Dict[str, Dict[str, float]]
    market_regime_history: List[Dict[str, Any]]
    neural_model_states: Dict[str, str]  # Model name -> checkpoint path
    configuration_evolution: List[Dict[str, Any]]
    error_patterns: Dict[str, int]
    successful_adaptations: List[Dict[str, Any]]

class PersistentLearningSystem:
    """Manages persistent learning across system restarts"""
    
    def __init__(self, data_dir: str = "data/learning"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths
        self.memory_file = self.data_dir / "system_memory.json"
        self.sessions_file = self.data_dir / "learning_sessions.json"
        self.neural_checkpoints_dir = self.data_dir / "neural_checkpoints"
        self.neural_checkpoints_dir.mkdir(exist_ok=True)
        
        # Current session
        self.current_session: Optional[LearningSession] = None
        self.system_memory: Optional[SystemMemory] = None
        self.session_start_time = datetime.now(timezone.utc)
        
        # Learning metrics
        self.learning_events = []
        self.adaptation_history = []
        
        logger.info("🧠 [PERSISTENT] Persistent learning system initialized")

    async def initialize(self) -> Dict[str, Any]:
        """Initialize the persistent learning system and load previous state"""
        try:
            # Load system memory
            self.system_memory = await self._load_system_memory()
            
            # Calculate time since last shutdown
            time_gap = await self._calculate_time_gap()
            
            # Start new learning session
            self.current_session = await self._start_new_session()
            
            # Load neural model states
            neural_states = await self._load_neural_model_states()
            
            # Analyze learning continuity
            continuity_analysis = await self._analyze_learning_continuity(time_gap)
            
            logger.info(f"🧠 [PERSISTENT] System restarted after {time_gap['hours']:.1f} hours")
            logger.info(f"🧠 [PERSISTENT] Total system experience: {self.system_memory.total_runtime_hours:.1f} hours")
            logger.info(f"🧠 [PERSISTENT] Total trades executed: {self.system_memory.total_trades}")
            logger.info(f"🧠 [PERSISTENT] Total profit generated: ${self.system_memory.total_profit:.2f}")
            
            return {
                'time_gap': time_gap,
                'system_memory': self.system_memory,
                'neural_states': neural_states,
                'continuity_analysis': continuity_analysis,
                'session_id': self.current_session.session_id
            }
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error initializing persistent learning: {e}")
            # Create default state
            self.system_memory = self._create_default_memory()
            self.current_session = await self._start_new_session()
            return {'error': str(e)}

    async def _load_system_memory(self) -> SystemMemory:
        """Load system memory from persistent storage"""
        try:
            if self.memory_file.exists():
                with open(self.memory_file, 'r') as f:
                    data = json.load(f)
                
                # Convert datetime strings back to datetime objects
                data['last_shutdown_time'] = datetime.fromisoformat(data['last_shutdown_time'])
                
                # Convert market regime history timestamps
                for regime in data.get('market_regime_history', []):
                    if 'timestamp' in regime:
                        regime['timestamp'] = datetime.fromisoformat(regime['timestamp'])
                
                return SystemMemory(**data)
            else:
                return self._create_default_memory()
                
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error loading system memory: {e}")
            return self._create_default_memory()

    def _create_default_memory(self) -> SystemMemory:
        """Create default system memory"""
        return SystemMemory(
            last_shutdown_time=datetime.now(timezone.utc),
            total_runtime_hours=0.0,
            total_trades=0,
            total_profit=0.0,
            learned_patterns={},
            strategy_performance={},
            market_regime_history=[],
            neural_model_states={},
            configuration_evolution=[],
            error_patterns={},
            successful_adaptations=[]
        )

    async def _calculate_time_gap(self) -> Dict[str, Any]:
        """Calculate time gap since last shutdown"""
        try:
            current_time = datetime.now(timezone.utc)
            last_shutdown = self.system_memory.last_shutdown_time
            
            time_diff = current_time - last_shutdown
            hours = time_diff.total_seconds() / 3600
            
            # Categorize the gap
            if hours < 1:
                gap_category = "short"
                impact = "minimal"
            elif hours < 24:
                gap_category = "medium"
                impact = "moderate"
            elif hours < 168:  # 1 week
                gap_category = "long"
                impact = "significant"
            else:
                gap_category = "very_long"
                impact = "major"
            
            return {
                'hours': hours,
                'days': hours / 24,
                'category': gap_category,
                'impact': impact,
                'last_shutdown': last_shutdown.isoformat(),
                'current_time': current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error calculating time gap: {e}")
            return {'hours': 0, 'category': 'unknown', 'impact': 'unknown'}

    async def _start_new_session(self) -> LearningSession:
        """Start a new learning session"""
        try:
            session_id = f"session_{int(time.time())}"
            
            return LearningSession(
                session_id=session_id,
                start_time=self.session_start_time,
                end_time=None,
                trades_executed=0,
                profit_generated=0.0,
                strategies_learned=[],
                market_conditions={},
                performance_metrics={},
                neural_weights_checksum="",
                version="1.0"
            )
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error starting new session: {e}")
            raise

    async def _load_neural_model_states(self) -> Dict[str, Any]:
        """Load neural model states from checkpoints"""
        try:
            neural_states = {}
            
            for model_name, checkpoint_path in self.system_memory.neural_model_states.items():
                checkpoint_file = self.neural_checkpoints_dir / checkpoint_path
                
                if checkpoint_file.exists():
                    try:
                        # Load checkpoint metadata
                        with open(checkpoint_file.with_suffix('.json'), 'r') as f:
                            metadata = json.load(f)
                        
                        neural_states[model_name] = {
                            'checkpoint_path': str(checkpoint_file),
                            'metadata': metadata,
                            'available': True
                        }
                        
                        logger.info(f"✅ [PERSISTENT] Loaded {model_name} checkpoint")
                        
                    except Exception as e:
                        logger.warning(f"⚠️ [PERSISTENT] Failed to load {model_name} checkpoint: {e}")
                        neural_states[model_name] = {'available': False, 'error': str(e)}
                else:
                    neural_states[model_name] = {'available': False, 'error': 'Checkpoint not found'}
            
            return neural_states
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error loading neural states: {e}")
            return {}

    async def _analyze_learning_continuity(self, time_gap: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze learning continuity and recommend adaptations"""
        try:
            analysis = {
                'continuity_score': 1.0,
                'recommendations': [],
                'required_adaptations': [],
                'market_drift_risk': 'low'
            }
            
            hours_gap = time_gap.get('hours', 0)
            
            # Assess continuity based on time gap
            if hours_gap > 168:  # More than 1 week
                analysis['continuity_score'] = 0.3
                analysis['market_drift_risk'] = 'high'
                analysis['recommendations'].extend([
                    'Retrain neural models with recent data',
                    'Recalibrate risk parameters',
                    'Update market regime detection'
                ])
                analysis['required_adaptations'].extend([
                    'market_regime_recalibration',
                    'neural_model_retraining',
                    'strategy_parameter_update'
                ])
            elif hours_gap > 24:  # More than 1 day
                analysis['continuity_score'] = 0.7
                analysis['market_drift_risk'] = 'medium'
                analysis['recommendations'].extend([
                    'Update market data cache',
                    'Validate strategy parameters'
                ])
                analysis['required_adaptations'].extend([
                    'market_data_refresh',
                    'parameter_validation'
                ])
            elif hours_gap > 1:  # More than 1 hour
                analysis['continuity_score'] = 0.9
                analysis['market_drift_risk'] = 'low'
                analysis['recommendations'].append('Quick market state validation')
                analysis['required_adaptations'].append('market_state_validation')
            
            # Check for significant market events during downtime
            market_events = await self._detect_market_events_during_downtime(time_gap)
            if market_events:
                analysis['continuity_score'] *= 0.8
                analysis['market_events'] = market_events
                analysis['recommendations'].append('Analyze market events impact')
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error analyzing learning continuity: {e}")
            return {'continuity_score': 0.5, 'error': str(e)}

    async def _detect_market_events_during_downtime(self, time_gap: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect significant market events during system downtime"""
        try:
            # This would integrate with market data providers to detect:
            # - Major price movements
            # - High volatility periods
            # - News events
            # - Regulatory changes
            
            # For now, return empty list (would be implemented with real data feeds)
            return []
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error detecting market events: {e}")
            return []

    async def record_learning_event(self, event_type: str, data: Dict[str, Any]):
        """Record a learning event"""
        try:
            learning_event = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'event_type': event_type,
                'data': data,
                'session_id': self.current_session.session_id if self.current_session else None
            }
            
            self.learning_events.append(learning_event)
            
            # Update current session
            if self.current_session:
                if event_type == 'trade_executed':
                    self.current_session.trades_executed += 1
                    self.current_session.profit_generated += data.get('profit', 0.0)
                elif event_type == 'strategy_learned':
                    strategy_name = data.get('strategy_name')
                    if strategy_name and strategy_name not in self.current_session.strategies_learned:
                        self.current_session.strategies_learned.append(strategy_name)
            
            logger.debug(f"📝 [PERSISTENT] Recorded learning event: {event_type}")
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error recording learning event: {e}")

    async def save_neural_checkpoint(self, model_name: str, model_state: Any, 
                                   metadata: Dict[str, Any]) -> str:
        """Save neural model checkpoint"""
        try:
            timestamp = int(time.time())
            checkpoint_filename = f"{model_name}_{timestamp}.pkl"
            checkpoint_path = self.neural_checkpoints_dir / checkpoint_filename
            
            # Save model state
            with open(checkpoint_path, 'wb') as f:
                pickle.dump(model_state, f)
            
            # Save metadata
            metadata_path = checkpoint_path.with_suffix('.json')
            metadata['timestamp'] = datetime.now(timezone.utc).isoformat()
            metadata['model_name'] = model_name
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Update system memory
            self.system_memory.neural_model_states[model_name] = checkpoint_filename
            
            logger.info(f"💾 [PERSISTENT] Saved {model_name} checkpoint: {checkpoint_filename}")
            
            return str(checkpoint_path)
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error saving neural checkpoint: {e}")
            raise

    async def shutdown_gracefully(self) -> Dict[str, Any]:
        """Gracefully shutdown and save all learning state"""
        try:
            shutdown_time = datetime.now(timezone.utc)
            
            # Finalize current session
            if self.current_session:
                self.current_session.end_time = shutdown_time
                session_duration = (shutdown_time - self.current_session.start_time).total_seconds() / 3600
                
                # Update system memory
                self.system_memory.last_shutdown_time = shutdown_time
                self.system_memory.total_runtime_hours += session_duration
                self.system_memory.total_trades += self.current_session.trades_executed
                self.system_memory.total_profit += self.current_session.profit_generated
            
            # Save system memory
            await self._save_system_memory()
            
            # Save learning session
            await self._save_learning_session()
            
            logger.info(f"🛑 [PERSISTENT] Graceful shutdown completed")
            logger.info(f"🛑 [PERSISTENT] Session duration: {session_duration:.2f} hours")
            logger.info(f"🛑 [PERSISTENT] Trades this session: {self.current_session.trades_executed}")
            logger.info(f"🛑 [PERSISTENT] Profit this session: ${self.current_session.profit_generated:.2f}")
            
            return {
                'success': True,
                'session_duration_hours': session_duration,
                'trades_executed': self.current_session.trades_executed,
                'profit_generated': self.current_session.profit_generated
            }
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error during graceful shutdown: {e}")
            return {'success': False, 'error': str(e)}

    async def _save_system_memory(self):
        """Save system memory to persistent storage"""
        try:
            # Convert datetime objects to ISO format for JSON serialization
            memory_dict = asdict(self.system_memory)
            memory_dict['last_shutdown_time'] = self.system_memory.last_shutdown_time.isoformat()
            
            # Convert market regime history timestamps
            for regime in memory_dict.get('market_regime_history', []):
                if 'timestamp' in regime and isinstance(regime['timestamp'], datetime):
                    regime['timestamp'] = regime['timestamp'].isoformat()
            
            with open(self.memory_file, 'w') as f:
                json.dump(memory_dict, f, indent=2, default=str)
            
            logger.debug("💾 [PERSISTENT] System memory saved")
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error saving system memory: {e}")

    async def _save_learning_session(self):
        """Save current learning session"""
        try:
            if not self.current_session:
                return
            
            # Load existing sessions
            sessions = []
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r') as f:
                    sessions = json.load(f)
            
            # Add current session
            session_dict = asdict(self.current_session)
            session_dict['start_time'] = self.current_session.start_time.isoformat()
            if self.current_session.end_time:
                session_dict['end_time'] = self.current_session.end_time.isoformat()
            
            sessions.append(session_dict)
            
            # Keep only last 100 sessions
            sessions = sessions[-100:]
            
            with open(self.sessions_file, 'w') as f:
                json.dump(sessions, f, indent=2)
            
            logger.debug("💾 [PERSISTENT] Learning session saved")

        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error saving learning session: {e}")


class FallbackPersistentLearning:
    """
    Fallback persistent learning system for when the main system is unavailable
    Provides essential learning persistence with simplified functionality
    """

    def __init__(self, data_dir: str = "data/fallback_learning"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # File paths
        self.session_file = self.data_dir / "session_data.json"
        self.emergency_file = self.data_dir / "emergency_state.json"

        # Current session data
        self.session_data = {
            'session_id': f"fallback_{int(time.time())}",
            'start_time': datetime.now(timezone.utc).isoformat(),
            'trades': [],
            'profits': [],
            'strategies_used': [],
            'performance_metrics': {},
            'learning_events': []
        }

        # Emergency state
        self.emergency_state = {
            'last_update': datetime.now(timezone.utc).isoformat(),
            'system_status': 'active',
            'critical_errors': [],
            'recovery_actions': [],
            'backup_data': {}
        }

        logger.info("🔄 [FALLBACK-LEARNING] Fallback persistent learning system initialized")

    async def update_session_data(self, data: Dict[str, Any]) -> None:
        """Update session data with new information"""
        try:
            # Update session data
            if 'trade' in data:
                self.session_data['trades'].append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'trade_data': data['trade']
                })

            if 'profit' in data:
                self.session_data['profits'].append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'profit': data['profit']
                })

            if 'strategy' in data:
                strategy = data['strategy']
                if strategy not in self.session_data['strategies_used']:
                    self.session_data['strategies_used'].append(strategy)

            if 'performance_metrics' in data:
                self.session_data['performance_metrics'].update(data['performance_metrics'])

            if 'learning_event' in data:
                self.session_data['learning_events'].append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'event': data['learning_event']
                })

            # Save to file
            await self._save_session_data()

            logger.debug("📝 [FALLBACK-LEARNING] Session data updated")

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error updating session data: {e}")

    async def save_emergency_state(self, state_data: Dict[str, Any]) -> None:
        """Save emergency state for system recovery"""
        try:
            # Update emergency state
            self.emergency_state.update({
                'last_update': datetime.now(timezone.utc).isoformat(),
                'system_status': state_data.get('status', 'unknown'),
                'backup_data': state_data
            })

            # Add critical errors if present
            if 'error' in state_data:
                self.emergency_state['critical_errors'].append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'error': state_data['error']
                })

            # Add recovery actions if present
            if 'recovery_action' in state_data:
                self.emergency_state['recovery_actions'].append({
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'action': state_data['recovery_action']
                })

            # Save to file
            await self._save_emergency_state()

            logger.info("🚨 [FALLBACK-LEARNING] Emergency state saved")

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error saving emergency state: {e}")

    async def _save_session_data(self) -> None:
        """Save session data to file"""
        try:
            with open(self.session_file, 'w') as f:
                json.dump(self.session_data, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error saving session data to file: {e}")

    async def _save_emergency_state(self) -> None:
        """Save emergency state to file"""
        try:
            with open(self.emergency_file, 'w') as f:
                json.dump(self.emergency_state, f, indent=2, default=str)

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error saving emergency state to file: {e}")

    async def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session"""
        try:
            total_trades = len(self.session_data['trades'])
            total_profit = sum(p['profit'] for p in self.session_data['profits'])
            strategies_count = len(self.session_data['strategies_used'])

            return {
                'session_id': self.session_data['session_id'],
                'start_time': self.session_data['start_time'],
                'total_trades': total_trades,
                'total_profit': total_profit,
                'strategies_count': strategies_count,
                'learning_events_count': len(self.session_data['learning_events']),
                'status': 'active'
            }

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error getting session summary: {e}")
            return {'error': str(e)}

    async def load_previous_state(self) -> Dict[str, Any]:
        """Load previous session state if available"""
        try:
            if self.session_file.exists():
                with open(self.session_file, 'r') as f:
                    previous_data = json.load(f)
                return previous_data

            return {}

        except Exception as e:
            logger.error(f"❌ [FALLBACK-LEARNING] Error loading previous state: {e}")
            return {}

    def get_learning_summary(self) -> Dict[str, Any]:
        """Get summary of learning progress"""
        try:
            if not self.system_memory or not self.current_session:
                return {'error': 'System not initialized'}
            
            current_time = datetime.now(timezone.utc)
            session_duration = (current_time - self.current_session.start_time).total_seconds() / 3600
            
            return {
                'total_experience_hours': self.system_memory.total_runtime_hours + session_duration,
                'total_trades': self.system_memory.total_trades + self.current_session.trades_executed,
                'total_profit': self.system_memory.total_profit + self.current_session.profit_generated,
                'current_session': {
                    'duration_hours': session_duration,
                    'trades': self.current_session.trades_executed,
                    'profit': self.current_session.profit_generated,
                    'strategies_learned': len(self.current_session.strategies_learned)
                },
                'learning_events_count': len(self.learning_events),
                'neural_models_available': len([m for m in self.system_memory.neural_model_states.keys()])
            }
            
        except Exception as e:
            logger.error(f"❌ [PERSISTENT] Error getting learning summary: {e}")
            return {'error': str(e)}
